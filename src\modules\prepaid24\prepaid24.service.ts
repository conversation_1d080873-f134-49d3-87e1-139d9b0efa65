import { Injectable, Logger } from '@nestjs/common';
import { allowedServiceMenus } from 'src/util/constants';

import { OzowApiService } from './ozow.api.service';
import { PrepaidApiService } from './prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { BotData } from '../shared/dtos/body-request.dto';

const listItemsPerPage = 20;

@Injectable()
export class Prepaid24Service {
  private readonly logger = new Logger(Prepaid24Service.name);
  constructor(
    private readonly prepaidApiSvc: PrepaidApiService,
    private readonly ozowApiSvc: OzowApiService,
    private readonly chatstackSvc: ChatstackService,
  ) {}

  formatAmount(amount: any) {
    return Number(amount).toLocaleString('en-ZA');
  }

  async verifyNumber(body: BotData): Promise<unknown> {
    const payload = body.payload;
    const phone = body.userId;

    let waNumber = payload.waNumber;

    if (!waNumber || waNumber == 'undefined') {
      waNumber = phone;
    }

    waNumber = waNumber.replace('+', '').replace('27', '0');

    await this.chatstackSvc.saveToInputGroup(body.inputGroupId, body.appKey, phone, {
      waNumber: waNumber,
    });

    return {
      stepId: payload.nextStepId,
    };
  }

  async verifyUser(body: BotData): Promise<unknown> {
    const payload = body.payload;
    const phone = body.userId;

    try {
      let cellnumber = payload.cellnumber;
      if (!cellnumber || cellnumber == 'undefined') {
        cellnumber = phone;
      }

      cellnumber = cellnumber.replace('+', '').replace('27', '0');

      const userDetails = await this.prepaidApiSvc.getClientDetails(cellnumber);
      if (userDetails.result !== 'Success') {
        return {
          stepId: payload.stepIdNotAuthorized,
        };
      }

      await this.chatstackSvc.saveToInputGroup(body.inputGroupId, body.appKey, phone, {
        balance: userDetails.balance,
        currBalance: this.formatAmount(userDetails.balance),
        name: userDetails.name,
        smartfund: userDetails.smartfund,
        cellnumber: userDetails.cellnumber,
        userDetails: userDetails,
        cart_details: [],
      });

      if (userDetails.smartfund == 'Yes') {
        return {
          stepId: payload.stepIdAuthorizedSmartFund,
        };
      }

      return {
        stepId: payload.stepIdAuthorized,
      };
    } catch (error: any) {
      this.logger.error('Failed to user details request', {
        error: error.message,
        stack: error.stack,
        response: error.response?.data,
        phone,
      });

      return {
        stepId: payload.stepIdNotAuthorized,
      };
    }
  }

  async cartDetails(body: BotData): Promise<unknown> {
    const payload = body.payload;
    const phone = body.userId;

    try {
      const cellnumber = payload.cellnumber.replace('+27', '0');

      const userDetails = await this.prepaidApiSvc.getClientDetails(cellnumber);
      if (userDetails.result !== 'Success') {
        return {
          stepId: payload.stepIdNotAuthorized,
        };
      }

      const inputGroupId = payload.inputGroupId ? payload.inputGroupId : body.inputGroupId;

      const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
        inputGroupId,
        body.appKey,
        phone,
      );

      if (!inputGroupValues.cart_details.length) {
        return {
          stepId: payload.stepIdEmptyCart,
        };
      }

      const cartTotalAmount = inputGroupValues.cart_details.reduce(
        (sum, item) => sum + item.amount * (item.quantity ?? 1),
        0,
      );
      if (payload.action == 'fund_check') {
        if (userDetails.balance < cartTotalAmount) {
          return {
            stepId: payload.stepIdNotEnoughBalance,
          };
        }

        return {
          stepId: payload.stepIdValid,
        };
      }

      // card details
      let message = `Here are the items in your cart:\n\n`;
      let cartPage = parseInt(payload?.cartPage == 'undefined' ? 0 : (payload?.cartPage ?? 0));

      if (payload?.pageReset == true) {
        cartPage = 0;
      }

      if (payload?.voucher == 'Previous list') cartPage = cartPage - 1;
      else cartPage = cartPage + 1;

      const totalPages = Math.ceil(inputGroupValues.cart_details.length / listItemsPerPage);
      if (cartPage > totalPages) {
        cartPage = totalPages;
      }

      if (cartPage <= 0) {
        cartPage = 1;
      }

      const startIndex = (cartPage - 1) * listItemsPerPage;
      const endIndex = startIndex + listItemsPerPage;
      const paginatedCartItems = inputGroupValues.cart_details.slice(startIndex, endIndex);

      message += paginatedCartItems
        .map((item, index) => {
          const quantity = item.quantity ?? 1;
          const quantityPart = quantity > 1 ? ` x ${quantity}` : '';
          const amount = this.formatAmount(item.amount * quantity);

          return `${index + 1}. *${item.product_name}*${quantityPart} \nAmount: R${amount}`;
        })
        .join('\n\n');

      message += `\n\n*Order Total: R${this.formatAmount(cartTotalAmount)}*`;

      let regex = 'Continue Shopping|Remove Cart Items|Check Out';
      const options = ['Continue Shopping', 'Remove Cart Items', 'Check Out'];
      const exitMap = {
        'Continue Shopping': payload.stepIdMainMenu,
        'Remove Cart Items': payload.stepManageCartId,
        'Check Out': payload.stepIdCheckout,
      };
      if (totalPages > 1 && cartPage < totalPages) {
        regex += '|Next items';
        options.push('Next items');
        exitMap['Next items'] = 'cart-items-more';
        message += `\n\nPlease select _*Next items*_ to load more items`;
      }

      this.chatstackSvc.saveToInputGroup(inputGroupId, body.appKey, phone, {
        cartPage: cartPage,
      });

      return {
        step: {
          id: payload.stepId,
          name: payload.stepId,
          entry: payload.stepId,
          message: message,
          type: 'inputOptions',
          options: options,
          exitMap: exitMap,
          validation: {
            message: 'Please select a valid option:',
            type: 'regex',
            regex: '^(' + regex + ')$',
          },
          inputGroupId: inputGroupId + 'Bot',
        },
      };
    } catch (error: any) {
      this.logger.error('Failed to user details request', {
        error: error.message,
        stack: error.stack,
        response: error.response?.data,
        phone,
      });

      return {
        stepId: 'other-error',
      };
    }
  }

  async cartCheckout(body: BotData): Promise<unknown> {
    const payload = body.payload;
    const phone = body.userId;

    try {
      const cellnumber = payload.cellnumber.replace('+27', '0');

      const userDetails = await this.prepaidApiSvc.getClientDetails(cellnumber);
      if (userDetails.result !== 'Success') {
        return {
          stepId: payload.stepIdNotAuthorized,
        };
      }

      const inputGroupId = payload.inputGroupId ? payload.inputGroupId : body.inputGroupId;
      const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
        inputGroupId,
        body.appKey,
        phone,
      );

      if (!inputGroupValues.cart_details.length) {
        return {
          stepId: payload.stepIdEmptyCart,
        };
      }

      const cartTotalAmount = inputGroupValues.cart_details.reduce(
        (sum, item) => sum + item.amount * (item.quantity ?? 1),
        0,
      );

      if (userDetails.balance < cartTotalAmount) {
        return {
          stepId: payload.stepIdNotEnoughBalance,
        };
      }

      // 1. create cart reference
      const cartReference = await this.prepaidApiSvc.getReference(cellnumber);
      if (!cartReference.reference) {
        return {
          stepId: payload.stepIdFailure,
        };
      }

      const expandedCart = inputGroupValues.cart_details.flatMap((item) => {
        const qty = item.quantity ?? 1; // Default to 1 if quantity is undefined

        return Array.from({ length: qty }, () => {
          const { quantity: _quantity, ...rest } = item;

          return { ...rest };
        });
      });

      // 2. create cart
      const cart = {
        uid: userDetails.uid,
        reference: cartReference.reference,
        cellnumber: cellnumber,
        amount: cartTotalAmount,
        pin: userDetails.smartfund == 'Yes' ? payload.smartfundPin : 'ozow',
        cart_details: expandedCart,
      };

      const createCart = await this.prepaidApiSvc.createCart(cart);
      if (createCart.result !== 'Success') {
        let message = createCart.reason;
        if (message.includes('Invalid smartfund pin')) {
          message = 'The SmartFund pin you entered is incorrect.';
        }

        return {
          step: {
            id: 'other-error',
            entry: 'other-error',
            name: 'other-error',
            message: 'Something went wrong!\n\n Error: ' + message,
            type: 'inputOptions',
            options: ['Try Again', 'Payment Options', 'Main Menu'],
            exitMap: {
              'Payment Options': payload.stepIdPaymentOptions,
              'Try Again': payload.stepIdTryAgain,
              'Main Menu': payload.stepIdMainMenu,
            },
            validation: {
              message: 'Please select a valid option:',
              type: 'regex',
              regex: '^(Try Again|Main Menu|Payment Options)$',
            },
            inputGroupId: inputGroupId + 'Bot',
          },
        };
      }

      // 3. check cart
      await this.prepaidApiSvc.checkCart(cartReference.reference);

      await this.chatstackSvc.saveToInputGroup(inputGroupId, body.appKey, phone, {
        cart_details: [],
      });

      return {
        stepId: payload.stepIdSuccess,
      };
    } catch (error: any) {
      this.logger.error('Failed to user details request', {
        error: error.message,
        stack: error.stack,
        response: error.response?.data,
        phone,
      });

      return {
        stepId: 'other-error',
      };
    }
  }

  async handleMenu(body: BotData): Promise<unknown> {
    const payload = body.payload;
    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      body.inputGroupId,
      body.appKey,
      body.userId,
    );
    try {
      const selectedProduct = JSON.parse(body.text);
      if (!allowedServiceMenus.includes(selectedProduct.service_menu)) {
        return {
          stepId: payload.invalidStepId,
        };
      }

      return {
        stepId: inputGroupValues.service_menu,
      };
    } catch (error) {
      this.logger.error('Not selected valid product', {
        error: error.message,
      });

      if (body.text == 'Yes') {
        return {
          stepId: inputGroupValues.service_menu,
        };
      }

      return {
        stepId: payload.invalidStepId,
      };
    }
  }

  async validateFlowResponse(body: BotData): Promise<unknown> {
    const { payload, previousStep } = body;
    try {
      const selectedProduct = JSON.parse(body.text);
      if (!selectedProduct.flow_token) {
        return {
          step: previousStep,
        };
      }

      return {
        stepId: payload.nextStepId,
      };
    } catch (error) {
      this.logger.log('Handling menu: ' + error.message);
      if (body.text.toLowerCase() == 'menu' || body.text == 'Yes') {
        return {
          stepId: payload.stepIdMainMenu,
        };
      }

      return {
        step: {
          ...previousStep,
          message: 'Please select valid response from Flow',
        },
      };
    }
  }

  async getOzowToken(body: BotData): Promise<unknown> {
    try {
      this.logger.log(body.contactNumber);

      return await this.ozowApiSvc.getToken();
    } catch (error) {
      this.logger.log('getOzowToken' + error.message);

      return error;
    }
  }
}
