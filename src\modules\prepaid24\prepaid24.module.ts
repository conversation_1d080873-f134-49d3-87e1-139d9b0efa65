import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EncryptionModule } from 'src/encryption/encryption.module';
import { PrepaidFlowController } from 'src/modules/flows/flow.controller';

import { OzowApiService } from './ozow.api.service';
import { PrepaidApiService } from './prepaid24.api.service';
import { Prepaid24Controller } from './prepaid24.controller';
import { PrepaidJourneyService } from './prepaid24.journey.service';
import { Prepaid24Service } from './prepaid24.service';
import { FlowModule } from '../flows/flow.module'; // Ensure correct path
import { ChatstackService } from '../shared/chatstack.service';

@Module({
  imports: [ConfigModule.forRoot(), HttpModule, forwardRef(() => FlowModule), EncryptionModule],
  controllers: [PrepaidFlowController, Prepaid24Controller], // Ensure HttpModule is imported
  providers: [
    PrepaidApiService,
    PrepaidJourneyService,
    ChatstackService,
    Prepaid24Service,
    OzowApiService,
  ],
  exports: [PrepaidApiService, PrepaidJourneyService, OzowApiService], // Ensure it's exported
})
export class Prepaid24Module {}
