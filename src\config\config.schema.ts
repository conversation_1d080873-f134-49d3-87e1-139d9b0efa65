import * as Jo<PERSON> from 'joi';

export const configValidationSchema = Joi.object({
  APP_NAME: Joi.string().default('CUSTOM_STEPS'),
  APP_PORT: Joi.number().default(3002).required(),
  CHATSTACK_API_URL: Joi.string().required(),
  CHATSOONER_URL: Joi.string().required(),
  PREPAID_API_BASE_URL: Joi.string().required(),
  PREPAID_API_MERCHANT: Joi.string().required(),
  PREPAID_API_PASSWORD: Joi.string().required(),
  APP_SECRET: Joi.string().required(),
  PRIVATE_KEY: Joi.string().required(),
  PASSPHRASE: Joi.string().required(),
  INPUT_GROUP_ID: Joi.string().required(),
  APP_KEY: Joi.string().required(),
});
