import { Injectable, Logger } from '@nestjs/common';
import { PP24_PROVIDERS } from 'src/util/constants';

import { EncryptionService } from '../../encryption/encryption.service';
import { AirtimeDataFlowService } from '../flows/airtime-data.flow.service';
import { CartFlowService } from '../flows/cart.flow.service';
import { DigitalFlowService } from '../flows/digital.flow.service';
import { DstvFlowService } from '../flows/dstv-flow.service';
import { EntertainmentFlowService } from '../flows/entertainment.flow.service';
import { GamingFlowService } from '../flows/gaming-voucher.flow.service';
import { GooglePlayFlowService } from '../flows/googleplay.flow.service';
import { MunicipalFlowService } from '../flows/municipal.flow.service';
import { NetflixFlowService } from '../flows/netflix.flow.service';
import { PrepaidFlowService } from '../flows/prepaid.flow.service';
import { RetailFlowService } from '../flows/retail-voucher.flow.service';
import { RegisterFlowService } from '../flows/register.flow.service';

@Injectable()
export class PrepaidJourneyService {
  private readonly logger = new Logger(PrepaidJourneyService.name);

  constructor(
    private readonly prepaidFlowService: PrepaidFlowService,
    private readonly netflixFlowService: NetflixFlowService,
    private readonly municipalFlowService: MunicipalFlowService,
    private readonly airtimeFlowService: AirtimeDataFlowService,
    private readonly cartFlowService: CartFlowService,
    private readonly digitalFlowService: DigitalFlowService,
    private readonly entertainmentFlowService: EntertainmentFlowService,
    private readonly encryptionService: EncryptionService,
    private readonly dstvService: DstvFlowService,
    private readonly googleplayService: GooglePlayFlowService,
    private readonly gamingVouchersService: GamingFlowService,
    private readonly retailVouchersService: RetailFlowService,
    private readonly registerFlowService: RegisterFlowService,
  ) {}

  async handleFlows(requestBody: any, flowName: string): Promise<any> {
    let decryptedRequest;
    try {
      decryptedRequest = this.decryptRequest(requestBody);

      this.logger.log(
        '✅ Decrypted Request Body:',
        JSON.stringify(decryptedRequest.decryptedBody, null, 2),
      );

      let screenResponse;
      switch (flowName) {
        case 'register':
          screenResponse = await this.registerFlowService.getNextScreen(
            decryptedRequest.decryptedBody,
          );
          break;

        case 'prepaid':
          screenResponse = await this.prepaidFlowService.getNextScreen(
            decryptedRequest.decryptedBody,
          );
          break;

        case 'netflix':
          screenResponse = await this.netflixFlowService.getNextScreen(
            decryptedRequest.decryptedBody,
          );
          break;

        case 'municipal':
          screenResponse = await this.municipalFlowService.getNextScreen(
            decryptedRequest.decryptedBody,
          );
          break;

        case 'airtime-data':
          screenResponse = await this.airtimeFlowService.getNextScreen(
            decryptedRequest.decryptedBody,
          );
          break;

        case 'cart-items':
          screenResponse = await this.cartFlowService.getItems(decryptedRequest.decryptedBody);
          break;

        case 'showmax':
          screenResponse = await this.digitalFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.SHOWMAX,
          );
          break;

        case 'uber':
          screenResponse = await this.digitalFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Uber,
          );
          break;

        case 'OTT':
          screenResponse = await this.entertainmentFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.OTT,
          );
          break;

        case 'Blu':
          screenResponse = await this.entertainmentFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Blu,
          );
          break;

        case 'dstv':
          screenResponse = await this.dstvService.getNextScreen(decryptedRequest.decryptedBody);
          break;
        case 'gaming':
          screenResponse = await this.gamingVouchersService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Gaming,
          );
          break;

        case 'retail':
          screenResponse = await this.retailVouchersService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Retail,
          );
          break;

        case 'google-play':
          screenResponse = await this.googleplayService.getNextScreen(
            decryptedRequest.decryptedBody,
          );
          break;

        case 'spotify':
          screenResponse = await this.digitalFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Spotify,
          );
          break;

        case 'microsoft':
          screenResponse = await this.digitalFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Microsoft,
          );
          break;

        case 'antivirus':
          screenResponse = await this.digitalFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Antivirus,
          );
          break;

        case 'entertainment':
          screenResponse = await this.entertainmentFlowService.managePackages(
            decryptedRequest.decryptedBody,
            PP24_PROVIDERS.Entertainment,
          );
          break;

        default:
          throw new Error('❌ Municipal Unhandled screen type.');
      }

      this.logger.log('✅ Screen Response:', JSON.stringify(screenResponse, null, 2));

      // ✅ Encrypt before sending response
      return this.encryptResponse(
        screenResponse,
        decryptedRequest.aesKeyBuffer,
        decryptedRequest.initialVectorBuffer,
      );
    } catch (error) {
      console.error('❌ Error processing request:', error);
      throw new Error('Failed to process request.');
    }
  }

  private decryptRequest(requestBody: any): any {
    try {
      return this.encryptionService.decryptRequest(requestBody);
    } catch (error) {
      console.error('❌ Decryption Error:', error);
      throw new Error('Decryption failed.');
    }
  }

  private encryptResponse(response: any, aesKeyBuffer: Buffer, initialVectorBuffer: Buffer): any {
    return this.encryptionService.encryptResponse(response, aesKeyBuffer, initialVectorBuffer);
  }
}
