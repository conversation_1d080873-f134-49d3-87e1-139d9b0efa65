import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';

@Injectable()
export class SessionGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const body = request.body;

    const session = body?.session;
    if (!session) {
      // Mark a missing session in the request for the interceptor to handle
      request.isVerifiedResponse = true;

      return true; // Allow the request to proceed
    }

    const currentStep = session?.session?.currentStep;
    const appKey = session?.appKey;
    if (!currentStep || !appKey) {
      throw new Error('Invalid session or missing properties.');
    }

    return true;
  }
}
