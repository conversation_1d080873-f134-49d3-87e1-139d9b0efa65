import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { DigitalRequestDataDto } from './dtos/digital.dto';
import { SCREEN_RESPONSES } from './screenResponses/digital';

@Injectable()
export class DigitalFlowService {
  private readonly logger = new Logger(DigitalFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }
  private userFlows: Record<string, any> = {};

  async dataHandling(provider: string): Promise<DigitalRequestDataDto> {
    const requestData: DigitalRequestDataDto = {
      packages: [],
    };

    try {
      const pkgResponse = await this.prepaidApiService.getDigital(provider);

      if (pkgResponse && pkgResponse.digital) {
        requestData.packages = pkgResponse.digital.map((product) => ({
          id: product.product_code.toString(),
          title: product.product_description,
          description: product.product_provider,
          metadata: 'R' + product.product_value,
        }));
      } else {
        console.error('API Error or Invalid Response:', pkgResponse);
      }

      return requestData;
    } catch (error) {
      console.error('Data handling failed:', error);

      return requestData;
    }
  }

  async managePackages(decryptedBody: any, flowType: string): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // Handle ping action
    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    let requestData = data?.data || data || {}; // Ensure safe data extraction

    // Handle client error scenarios
    if (requestData?.error) {
      console.warn('Digital Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      const apiData = await this.dataHandling(flowType);
      requestData = { ...requestData, ...apiData };
    }

    // Ensure INIT always sends a valid screen
    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.PACKAGE_VIEW,
        data: {
          ...SCREEN_RESPONSES.PACKAGE_VIEW.data,
          package_list: [...requestData.packages],
        },
      };
    }

    if (!screen) {
      console.error('Error: Digital Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    // Ensure a user flow exists for the current session
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // Handle screen transitions
    if (action === 'data_exchange') {
      switch (screen) {
        case 'PACKAGE_VIEW':
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            package: requestData.package ?? null,
            quantity: requestData.quantity ?? 1,
            amount: requestData.amount ?? null,
          };
          const flowData = this.userFlows[flow_token];

          const selectedPackage = requestData.packages?.find((p) => p.id === flowData.package);

          if (!selectedPackage) {
            throw new Error(`Invalid package selection: ${flowData.package}`);
          }

          if (flowData.package == '************') {
            selectedPackage.title = 'Uber Own Amount';
            selectedPackage.metadata = flowData.amount;
          }

          const cartItem = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Digital',
            provider: selectedPackage.description,
            product_code: selectedPackage.id || '',
            product_name: selectedPackage.title || 'Digital',
            amount: Number(selectedPackage.metadata.replace(/[^\d.]/g, '')),
            quantity: flowData.quantity,
          };

          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItem);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: {
                  flow_token,
                },
              },
            },
          };

        default:
          throw new Error('Digital Unhandled screen type.');
      }
    }
  }
}
