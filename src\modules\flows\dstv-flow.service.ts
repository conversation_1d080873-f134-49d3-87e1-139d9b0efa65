import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { SCREEN_RESPONSES } from './screenResponses/dstv';

@Injectable()
export class DstvFlowService {
  private readonly logger = new Logger(DstvFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }
  private userFlows: Record<string, any> = {};

  async getNextScreen(decryptedBody: any): Promise<any> {
    this.logger.debug('getNextScreen called with:', JSON.stringify(decryptedBody));
    const { screen, data, version, action, flow_token } = decryptedBody;

    let requestData = data?.data || data || {};

    // Handle ping action
    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    // Handle client error scenarios
    if (requestData?.error) {
      this.logger.warn('DSTV Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    // Retrieve input group values
    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      requestData = { ...requestData, ...decryptedBody.data };
    }

    // INIT always sends a valid screen
    if (action === 'INIT') {
      // Fetch DSTV accounts for the user's cell number for dropdown population
      const dstvResponse = await this.prepaidApiService.getDstvAccounts(
        inputGroupValues.cellnumber,
      );
      let accountOptions = [{ id: 'default', title: 'Select Account', enabled: false }];
      if (dstvResponse.result === 'Success' && dstvResponse.dstv.length > 0) {
        accountOptions = [
          { id: 'default', title: 'Select Account', enabled: false },
          ...dstvResponse.dstv.map((account) => ({
            id: account.account,
            title: account.account, // Always show account number
            enabled: true,
          })),
        ];
      }

      return {
        ...SCREEN_RESPONSES.DSTV,
        data: {
          ...SCREEN_RESPONSES.DSTV.data,
          account_options: accountOptions,
        },
      };
    }

    if (!screen) {
      this.logger.error('❌ Error: DSTV Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    // Ensure a user flow exists for the current session
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // Handle DSTV add to cart
    if (action === 'data_exchange' && screen === 'DSTV') {
      // Fetch DSTV accounts for the user's cell number
      const dstvResponse = await this.prepaidApiService.getDstvAccounts(
        inputGroupValues.cellnumber,
      );
      const accountOptions = [
        { id: 'default', title: 'Select Account', enabled: false },
        ...dstvResponse.dstv.map((account) => ({
          id: account.account,
          title: account.account,
          enabled: true,
        })),
      ];
      // Validate account selection
      const selectedAccount = requestData.account_number;
      if (!selectedAccount || selectedAccount === 'default') {
        return {
          ...SCREEN_RESPONSES.DSTV,
          data: {
            ...SCREEN_RESPONSES.DSTV.data,
            account_options: accountOptions,
            error_message: 'Please select a valid DSTV account.',
          },
        };
      }

      // Validate account exists
      const dstvAccount = dstvResponse.dstv.find((a) => a.account === selectedAccount);
      if (!dstvAccount) {
        return {
          ...SCREEN_RESPONSES.DSTV,
          data: {
            ...SCREEN_RESPONSES.DSTV.data,
            account_options: accountOptions,
            error_message: 'Invalid DSTV account selected.',
          },
        };
      }

      // Validate amount
      const amount = Number(requestData.amount);
      if (isNaN(amount) || amount < 20 || amount > Infinity) {
        return {
          ...SCREEN_RESPONSES.DSTV,
          data: {
            ...SCREEN_RESPONSES.DSTV.data,
            account_options: accountOptions,
            error_message: 'Please enter a valid amount more than R20.',
          },
        };
      }

      // Add to cart
      const cartItems = [
        {
          uid: inputGroupValues.userDetails.uid,
          cellnumber: inputGroupValues.userDetails.cellnumber,
          service: 'Dstv',
          provider:
            dstvAccount.description && dstvAccount.description.trim() !== ''
              ? dstvAccount.description
              : 'Dstv',
          product_code: dstvAccount.account,
          product_name: `Dstv - R${amount}`,
          amount,
        },
      ];
      this.logger.debug('🧺 Final cart items:', JSON.stringify(cartItems, null, 2));
      await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItems);

      // Return success screen
      return {
        ...SCREEN_RESPONSES.SUCCESS,
        data: {
          ...SCREEN_RESPONSES.SUCCESS.data,
          extension_message_response: {
            params: { flow_token },
          },
        },
      };
    }

    this.logger.error('❌ Unhandled action or screen:', action, screen);
    throw new Error('❌ Unhandled action or screen.');
  }
}
