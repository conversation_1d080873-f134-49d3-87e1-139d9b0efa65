import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable, of } from 'rxjs';

@Injectable()
export class VerifiedInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    if (request.isVerifiedResponse) {
      const response = context.switchToHttp().getResponse();
      response.status(200);

      return of({ status: 'verified' });
    }

    return next.handle();
  }
}
