export const SCREEN_RESPONSES = {
  PREPAID: {
    screen: 'PREPAID',
    data: {
      meter_list: [
        {
          id: 'default',
          title: 'Select a Meter',
        },
      ],
    },
  },
  PAY_ARREARS: {
    screen: 'PAY_ARREARS',
    data: {
      meter: '${form.meter}',
      electricity_arrears: '',
      rates_arrears: '',
      electricity_current: '',
      rates_current: '',
      init_rates: '0',
      init_electricity: '0',
    },
  },
  PURCHASE: {
    screen: 'PURCHASE',
    data: {
      electricity_arrears_amount: '${form.electricity_arrears_amount}',
      rates_arrears_amount: '${form.rates_arrears_amount}',
      electricity_current_amount: '${form.electricity_current_amount}',
      rates_current_amount: '${form.rates_current_amount}',
      meter: '${form.meter}',
      meter_number: '',
      paid_accounts: '',
      is_optional: true,
    },
  },
  SUCCESS: {
    screen: 'SUCCESS',
    data: {
      extension_message_response: {
        params: {
          flow_token: 'REPLACE_FLOW_TOKEN',
          some_param_name: 'PASS_CUSTOM_VALUE',
        },
      },
    },
  },
};
