import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SCREEN_RESPONSES } from './screenResponses/airtime-data';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';

@Injectable()
export class AirtimeDataFlowService {
  private readonly logger = new Logger(AirtimeDataFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  private userFlows: Record<string, any> = {};

  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }

  async dataHandling(cell: string): Promise<any> {
    try {
      // Fetch cell numbers, data products, and airtime products in parallel
      const [cellData, dataResponse] = await Promise.all([
        this.prepaidApiService.getCellNumbers(cell),
        this.prepaidApiService.getDataProducts('all'),
      ]);

      // Also fetch airtime products - we use the same API but with 'airtime' as the provider
      const airtimeResponse = await this.prepaidApiService.getDataProducts('airtime');

      const allDataProducts = dataResponse?.data || [];
      const allAirtimeProducts = airtimeResponse?.data || [];

      // Get unique providers from data products
      const allProviders = Array.from(
        new Set((allDataProducts || []).map((p: any) => p.product_provider)),
      ).filter(Boolean);

      const provider_options = [
        { id: 'default', title: 'Select a Provider' },
        ...allProviders.map((n: string) => ({ id: n, title: n })),
      ];

      const mobile_options = (cellData.cellnumbers || []).map((n: any) => ({
        id: n.cellnumber,
        title: n.description ? `${n.cellnumber} - ${n.description}` : `${n.cellnumber}`,
        description: n.network,
      }));

      // Format airtime options similar to data options
      const airtime_options = (allAirtimeProducts || []).map((opt: any, idx: number) => ({
        id: opt.product_code || String(idx + 1),
        title: opt.product_description,
        description: opt.product_provider,
        metadata: `R${opt.product_value}`,
      }));

      return {
        mobile_options,
        all_data_products: allDataProducts,
        all_airtime_products: allAirtimeProducts,
        provider_options,
        airtime_options,
      };
    } catch (error) {
      this.logger.error('❌ Failed to fetch data in dataHandling()', error);

      return {
        mobile_options: [],
        all_data_products: [],
        all_airtime_products: [],
        provider_options: [],
        airtime_options: [],
      };
    }
  }

  async getNextScreen(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;
    let requestData = data?.data || data || {};

    // Log the incoming request data for debugging
    this.logger.debug('Incoming request data:', JSON.stringify(requestData, null, 2));
    this.logger.debug('Action:', action, 'Screen:', screen);

    // Check if this is an "Add to Cart" action
    if (
      (requestData.data_option || (requestData.type === 'airtime' && requestData.amount)) &&
      !requestData.submit
    ) {
      // Only add submit flag if this is a fresh request with a data_option or airtime amount
      // This prevents auto-resubmission when reopening the flow
      if (action === 'data_exchange' && screen === 'ONE') {
        this.logger.debug('Detected Add to Cart action without submit flag, adding it');
        requestData.submit = true;
      }
    }

    // Clear any previous cart submission flags if this is an INIT action
    // This prevents resubmission when reopening the flow
    if (action === 'INIT') {
      this.logger.debug('INIT action detected, clearing any previous submission flags');
      if (this.userFlows[flow_token]) {
        delete this.userFlows[flow_token].submit;
        delete this.userFlows[flow_token].data_option;
        delete this.userFlows[flow_token].selected_data_option;
        delete this.userFlows[flow_token].airtime_option;
        delete this.userFlows[flow_token].selected_airtime_option;
      }
    }

    // Check for duplicate submissions by comparing with previous request
    if (
      this.userFlows[flow_token] &&
      ((requestData.data_option &&
        this.userFlows[flow_token].last_data_option === requestData.data_option) ||
        (requestData.type === 'airtime' &&
          requestData.amount &&
          this.userFlows[flow_token].last_airtime_amount === requestData.amount &&
          this.userFlows[flow_token].last_mobile_number === requestData.mobile_number))
    ) {
      this.logger.warn('Detected duplicate submission, clearing submit flag');
      requestData.submit = false;
    } else {
      // Store current options to detect duplicates in future requests
      if (!this.userFlows[flow_token]) this.userFlows[flow_token] = {};

      if (requestData.data_option) {
        this.userFlows[flow_token].last_data_option = requestData.data_option;
      }

      if (requestData.type === 'airtime' && requestData.amount) {
        this.userFlows[flow_token].last_airtime_amount = requestData.amount;
        this.userFlows[flow_token].last_mobile_number = requestData.mobile_number;
      }
    }

    if (action === 'ping') {
      return { version, data: { status: 'active' } };
    }

    if (requestData?.error) {
      return { version, data: { acknowledged: true } };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      const apiData = await this.dataHandling(inputGroupValues.cellnumber);
      requestData = { ...requestData, ...apiData };
    }

    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    this.userFlows[flow_token] = { ...this.userFlows[flow_token], ...requestData };
    requestData = { ...this.userFlows[flow_token] };

    let init_provider = this.userFlows[flow_token]?.init_provider || '';
    let selectedProvider =
      requestData.service_provider && requestData.service_provider !== 'default'
        ? requestData.service_provider
        : init_provider || '';

    if (requestData.service_provider && requestData.service_provider !== 'default') {
      requestData.init_provider = requestData.service_provider;
    }

    // --- INIT action: always start with main screen ---
    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.ONE,
        data: {
          ...SCREEN_RESPONSES.ONE.data,
          provider_options: requestData.provider_options || [],
          range_options: this.getRangeOptions(requestData.all_data_products, 'All'),
          category_options: this.getCategoryOptions(requestData.all_data_products, 'All', ''),
          mobile_options: [
            ...SCREEN_RESPONSES.ONE.data.mobile_options,
            ...(requestData.mobile_options || []),
            { id: 'add_new', title: 'Add New Cell Number' },
          ],
          data_options: this.getFilteredDataOptions(requestData, 'All', '', ''),
          airtime_options: requestData.airtime_options || [],
        },
      };
    }

    // --- Ensure a user flow exists for the current session ---
    // --- Mobile number/provider sync logic ---
    if (requestData.trigger === 'number_selected' && requestData.mobile_number) {
      const selectedMobile = (requestData.mobile_options || []).find(
        (m: any) => m.id === requestData.mobile_number,
      );
      if (selectedMobile && selectedMobile.description) {
        init_provider = selectedMobile.description;
        requestData.service_provider = selectedMobile.description;
      }
    }

    if (requestData.trigger === 'provider_selected' && requestData.service_provider) {
      init_provider = requestData.service_provider;
    }

    if (requestData.trigger === 'number_selected' || requestData.trigger === 'provider_selected') {
      requestData.init_provider = init_provider;
    }

    if (requestData.trigger === 'range_selected' || requestData.trigger === 'category_selected') {
      // If provider is empty or default, but mobile_number is present, always restore provider from mobile_options
      if (!requestData.service_provider || requestData.service_provider === 'default') {
        if (requestData.mobile_number) {
          const selectedMobile = (requestData.mobile_options || []).find(
            (m: any) => m.id === requestData.mobile_number,
          );
          if (selectedMobile && selectedMobile.description) {
            selectedProvider = selectedMobile.description;
            requestData.service_provider = selectedMobile.description;
            requestData.init_provider = selectedMobile.description;
          } else if (init_provider) {
            selectedProvider = init_provider;
            requestData.service_provider = init_provider;
          }
        } else if (init_provider) {
          selectedProvider = init_provider;
          requestData.service_provider = init_provider;
        }
      } else {
        selectedProvider = requestData.service_provider;
      }

      if (init_provider) {
        requestData.init_provider = init_provider;
      }
    }

    if ((!selectedProvider || selectedProvider === 'default') && requestData.mobile_number) {
      const selectedMobile = (requestData.mobile_options || []).find(
        (m: any) => m.id === requestData.mobile_number,
      );
      if (selectedMobile && selectedMobile.description) {
        selectedProvider = selectedMobile.description;
        requestData.service_provider = selectedMobile.description;
        requestData.init_provider = selectedMobile.description;
      }
    }

    if ((!selectedProvider || selectedProvider === 'default') && init_provider) {
      selectedProvider = init_provider;
      requestData.service_provider = init_provider;
    }

    // Always sync init_provider with service_provider if service_provider is set and not 'default'
    if (requestData.service_provider && requestData.service_provider !== 'default') {
      requestData.init_provider = requestData.service_provider;
    }

    // --- Handle data_exchange actions ---
    if (action === 'data_exchange') {
      switch (screen) {
        case 'ONE': {
          // Special handling for airtime with custom amount
          if (requestData.type === 'airtime' && requestData.amount && requestData.submit === true) {
            try {
              const baseCartItem = {
                uid: inputGroupValues.userDetails?.uid,
                cellnumber: inputGroupValues.userDetails?.cellnumber || inputGroupValues.cellnumber,
              };

              // Validate mobile number - but don't block if it's already set in the UI
              if (!requestData.mobile_number || requestData.mobile_number === 'default') {
                this.logger.warn(
                  'No mobile number selected for airtime product, but continuing anyway',
                );
              }

              const cartItems = [
                {
                  ...baseCartItem,
                  provider: requestData.service_provider || '',
                  service: 'Airtime',
                  number: requestData.mobile_number || '',
                  product_code: `airtime-${Date.now()}`, // Generate a unique product code
                  product_name: `Airtime R${requestData.amount}`,
                  amount: Number(requestData.amount),
                },
              ];

              this.logger.debug(
                '🧺 Final custom airtime cart items:',
                JSON.stringify(cartItems, null, 2),
              );

              // Check if this item is already in the cart to prevent duplicates
              const existingCartItems =
                (await this.chatstackSvc.retrieveFromInputGroup(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  'cart_details',
                )) || [];

              this.logger.debug('Existing cart items:', JSON.stringify(existingCartItems, null, 2));

              // For custom airtime, we don't check for duplicates by product_code since it's generated
              // Instead, we check for duplicates by service, number, and amount
              const isDuplicate = existingCartItems.some(
                (item: any) =>
                  item.service === 'Airtime' &&
                  item.number === cartItems[0].number &&
                  item.amount === cartItems[0].amount,
              );

              if (isDuplicate) {
                this.logger.warn('Duplicate custom airtime item detected, skipping cart addition');
              } else {
                // Add to cart
                await this.chatstackSvc.addToCart(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  cartItems,
                );
              }

              // Clear the submission flags to prevent resubmission
              if (this.userFlows[flow_token]) {
                delete this.userFlows[flow_token].submit;
                delete this.userFlows[flow_token].amount;
              }

              this.logger.debug('Custom airtime product added to cart successfully');

              // Go to SUCCESS screen
              return {
                ...SCREEN_RESPONSES.SUCCESS,
                data: {
                  ...SCREEN_RESPONSES.SUCCESS.data,
                  extension_message_response: {
                    params: { flow_token },
                  },
                },
              };
            } catch (error) {
              this.logger.error('Error adding custom airtime product to cart:', error);

              return {
                ...SCREEN_RESPONSES.ONE,
                data: {
                  ...SCREEN_RESPONSES.ONE.data,
                  error_message: 'Failed to add airtime product to cart. Please try again.',
                },
              };
            }
          }

          // Filtering logic
          const selectedRange =
            requestData.range && requestData.range !== 'default' ? requestData.range : 'All';
          const selectedCategory =
            requestData.category && requestData.category !== 'default' ? requestData.category : '';
          const filteredDataOptions = this.getFilteredDataOptions(
            requestData,
            selectedRange,
            selectedProvider,
            selectedCategory,
          );
          const range_options = this.getRangeOptions(
            requestData.all_data_products,
            selectedProvider,
          );
          const category_options = this.getCategoryOptions(
            requestData.all_data_products,
            selectedProvider,
            selectedRange,
          );
          const provider_options = requestData.provider_options || [];

          // If user is submitting a product selection, add to cart and return SUCCESS
          // Check for both submit flag and data_option/selected_data_option
          if (
            (requestData.submit === true || requestData.data_option) &&
            (requestData.selected_data_option || requestData.data_option)
          ) {
            // Map data_option to selected_data_option if needed
            if (!requestData.selected_data_option && requestData.data_option) {
              requestData.selected_data_option = requestData.data_option;
            }

            try {
              const baseCartItem = {
                uid: inputGroupValues.userDetails?.uid,
                cellnumber: inputGroupValues.userDetails?.cellnumber || inputGroupValues.cellnumber,
              };

              // Log the data option being used
              this.logger.debug('Using data option:', requestData.selected_data_option);

              // Find the selected product from all_data_products
              const selectedOption = (requestData.all_data_products || []).find(
                (opt: any) => opt.product_code === requestData.selected_data_option,
              );

              this.logger.debug(
                'Selected product:',
                selectedOption ? JSON.stringify(selectedOption, null, 2) : 'Not found',
              );

              if (!selectedOption) {
                this.logger.error(`Invalid product selection: ${requestData.selected_data_option}`);
                throw new Error(`Invalid product selection: ${requestData.selected_data_option}`);
              }

              // Validate mobile number - but don't block if it's already set in the UI
              if (!requestData.mobile_number || requestData.mobile_number === 'default') {
                this.logger.warn(
                  'No mobile number selected for data product, but continuing anyway',
                );
                // Don't throw an error here as the mobile number might be set in the UI
              }

              const cartItems = [
                {
                  ...baseCartItem,
                  provider: selectedOption.product_provider || selectedProvider || '',
                  service: 'Data',
                  number: requestData.mobile_number || '',
                  product_code: selectedOption.product_code,
                  product_name: selectedOption.product_description,
                  amount: Number(selectedOption.product_value),
                },
              ];

              this.logger.debug('🧺 Final cart items:', JSON.stringify(cartItems, null, 2));

              // Check if this item is already in the cart to prevent duplicates
              const existingCartItems =
                (await this.chatstackSvc.retrieveFromInputGroup(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  'cart_details',
                )) || [];

              this.logger.debug('Existing cart items:', JSON.stringify(existingCartItems, null, 2));

              // Check for duplicates based on product_code and number
              const isDuplicate = existingCartItems.some(
                (item: any) =>
                  item.product_code === cartItems[0].product_code &&
                  item.number === cartItems[0].number &&
                  item.service === cartItems[0].service,
              );

              if (isDuplicate) {
                this.logger.warn('Duplicate data item detected, skipping cart addition');
              } else {
                // Add to cart
                await this.chatstackSvc.addToCart(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  cartItems,
                );
              }

              // Clear the submission flags to prevent resubmission
              if (this.userFlows[flow_token]) {
                delete this.userFlows[flow_token].submit;
                delete this.userFlows[flow_token].data_option;
                delete this.userFlows[flow_token].selected_data_option;
              }

              this.logger.debug('Data product added to cart successfully');

              // Go to SUCCESS screen
              return {
                ...SCREEN_RESPONSES.SUCCESS,
                data: {
                  ...SCREEN_RESPONSES.SUCCESS.data,
                  extension_message_response: {
                    params: { flow_token },
                  },
                },
              };
            } catch (error) {
              this.logger.error('Error adding data product to cart:', error);

              return {
                ...SCREEN_RESPONSES.ONE,
                data: {
                  ...SCREEN_RESPONSES.ONE.data,
                  error_message: 'Failed to add data product to cart. Please try again.',
                  provider_options: requestData.provider_options || [],
                  range_options: this.getRangeOptions(requestData.all_data_products, 'All'),
                  category_options: this.getCategoryOptions(
                    requestData.all_data_products,
                    'All',
                    '',
                  ),
                  mobile_options: [
                    ...SCREEN_RESPONSES.ONE.data.mobile_options,
                    ...(requestData.mobile_options || []),
                    { id: 'add_new', title: 'Add New Cell Number' },
                  ],
                  data_options: this.getFilteredDataOptions(requestData, 'All', '', ''),
                },
              };
            }
          }

          // Normal screen render
          let service_provider_to_send = selectedProvider;
          if (!service_provider_to_send || service_provider_to_send === 'default') {
            service_provider_to_send = requestData.init_provider || '';
          }

          // Always sync init_provider with service_provider before returning
          if (service_provider_to_send && service_provider_to_send !== 'default') {
            requestData.init_provider = service_provider_to_send;
          }

          return {
            ...SCREEN_RESPONSES.ONE,
            data: {
              ...SCREEN_RESPONSES.ONE.data,
              provider_options,
              range_options,
              category_options,
              mobile_options: [
                ...SCREEN_RESPONSES.ONE.data.mobile_options,
                ...(requestData.mobile_options || []),
                { id: 'add_new', title: 'Add New Cell Number' },
              ],
              data_options: filteredDataOptions,
              error_message: undefined,
              init_provider: requestData.init_provider,
              service_provider: service_provider_to_send,
              range: selectedRange,
              category: selectedCategory || 'default',
              mobile_number: requestData.mobile_number || '',
            },
          };
        }

        case 'AIRTIME': {
          // Check for both submit flag and airtime_option/selected_airtime_option
          if (
            (requestData.submit === true || requestData.airtime_option) &&
            (requestData.selected_airtime_option || requestData.airtime_option)
          ) {
            // Map airtime_option to selected_airtime_option if needed
            if (!requestData.selected_airtime_option && requestData.airtime_option) {
              requestData.selected_airtime_option = requestData.airtime_option;
            }

            try {
              const baseCartItem = {
                uid: inputGroupValues.userDetails?.uid,
                cellnumber: inputGroupValues.userDetails?.cellnumber || inputGroupValues.cellnumber,
              };

              // Find the selected airtime product
              const selectedOption = (requestData.all_airtime_products || []).find(
                (opt: any) => opt.product_code === requestData.selected_airtime_option,
              );

              if (!selectedOption) {
                this.logger.error(
                  `Invalid airtime product selection: ${requestData.selected_airtime_option}`,
                );
                throw new Error(
                  `Invalid airtime product selection: ${requestData.selected_airtime_option}`,
                );
              }

              // Validate mobile number - but don't block if it's already set in the UI
              if (!requestData.mobile_number || requestData.mobile_number === 'default') {
                this.logger.warn(
                  'No mobile number selected for airtime product, but continuing anyway',
                );
                // Don't throw an error here as the mobile number might be set in the UI
              }

              const cartItems = [
                {
                  ...baseCartItem,
                  provider: selectedOption.product_provider || selectedProvider || '',
                  service: 'Airtime',
                  number: requestData.mobile_number || '',
                  product_code: requestData.mobile_number || '',
                  product_name: selectedOption.product_description,
                  amount: Number(selectedOption.product_value),
                },
              ];

              this.logger.debug('🧺 Final airtime cart items:', JSON.stringify(cartItems, null, 2));

              // Check if this item is already in the cart to prevent duplicates
              const existingCartItems =
                (await this.chatstackSvc.retrieveFromInputGroup(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  'cart_details',
                )) || [];

              this.logger.debug('Existing cart items:', JSON.stringify(existingCartItems, null, 2));

              // Check for duplicates based on product_code and number
              const isDuplicate = existingCartItems.some(
                (item: any) =>
                  item.product_code === cartItems[0].product_code &&
                  item.number === cartItems[0].number &&
                  item.service === cartItems[0].service,
              );

              if (isDuplicate) {
                this.logger.warn('Duplicate airtime item detected, skipping cart addition');
              } else {
                // Add to cart
                await this.chatstackSvc.addToCart(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  cartItems,
                );
              }

              // Clear the submission flags to prevent resubmission
              if (this.userFlows[flow_token]) {
                delete this.userFlows[flow_token].submit;
                delete this.userFlows[flow_token].airtime_option;
                delete this.userFlows[flow_token].selected_airtime_option;
              }

              this.logger.debug('Airtime product added to cart successfully');

              // Go to SUCCESS screen
              return {
                ...SCREEN_RESPONSES.SUCCESS,
                data: {
                  ...SCREEN_RESPONSES.SUCCESS.data,
                  extension_message_response: {
                    params: { flow_token },
                  },
                },
              };
            } catch (error) {
              this.logger.error('Error adding airtime product to cart:', error);

              return {
                ...SCREEN_RESPONSES.ONE,
                data: {
                  ...SCREEN_RESPONSES.ONE.data,
                  error_message: 'Failed to add airtime product to cart. Please try again.',
                },
              };
            }
          }

          // If we reach here, return the AIRTIME screen with appropriate options
          return {
            ...SCREEN_RESPONSES.AIRTIME,
            data: {
              ...SCREEN_RESPONSES.AIRTIME.data,
              service_provider: selectedProvider,
              mobile_number: requestData.mobile_number || '',
              mobile_options: [
                ...SCREEN_RESPONSES.ONE.data.mobile_options,
                ...(requestData.mobile_options || []),
                { id: 'add_new', title: 'Add New Cell Number' },
              ],
              airtime_options: requestData.airtime_options || [],
            },
          };
        }

        default:
          throw new Error('❌ Unhandled screen type.');
      }
    }

    // --- Fallback: return main screen ---
    // Always sync init_provider with service_provider before returning
    if (requestData.service_provider && requestData.service_provider !== 'default') {
      requestData.init_provider = requestData.service_provider;
    }

    return {
      ...SCREEN_RESPONSES.ONE,
      data: {
        ...SCREEN_RESPONSES.ONE.data,
        provider_options: requestData.provider_options || [],
        range_options: this.getRangeOptions(requestData.all_data_products, 'All'),
        category_options: this.getCategoryOptions(requestData.all_data_products, 'All', ''),
        mobile_options: [
          ...SCREEN_RESPONSES.ONE.data.mobile_options,
          ...(requestData.mobile_options || []),
          { id: 'add_new', title: 'Add New Cell Number' },
        ],
        data_options: this.getFilteredDataOptions(requestData, 'All', '', ''),
        airtime_options: requestData.airtime_options || [],
        init_provider: requestData.init_provider,
        service_provider: requestData.service_provider,
        range: requestData.range || 'All',
        category: requestData.category || 'default',
        mobile_number: requestData.mobile_number || '',
      },
    };
  }

  // --- Helper: Filter data options ---
  private getFilteredDataOptions(
    requestData: any,
    selectedRange: string,
    selectedProvider: string,
    selectedCategory: string,
  ) {
    let filtered = (requestData.all_data_products || []).map((opt: any, idx: number) => ({
      id: opt.product_code || String(idx + 1),
      title: opt.product_description,
      description: opt.product_provider,
      metadata: `R${opt.product_value}`,
    }));
    if (selectedProvider && selectedProvider !== 'default') {
      filtered = filtered.filter(
        (opt: any) => opt.description?.toLowerCase() === selectedProvider.toLowerCase(),
      );
    }

    if (selectedRange && selectedRange !== 'All' && selectedRange !== 'default') {
      const priceRanges = [
        { id: 'All', title: 'All', min: 0, max: Infinity },
        { id: '0-50', title: 'R0 - R50', min: 0, max: 50 },
        { id: '51-100', title: 'R51 - R100', min: 51, max: 100 },
        { id: '101-500', title: 'R101 - R500', min: 101, max: 500 },
        { id: '500+', title: 'R500+', min: 501, max: Infinity },
      ];
      const rangeObj = priceRanges.find((r) => r.id === selectedRange || r.title === selectedRange);
      if (rangeObj) {
        filtered = filtered.filter((opt: any) => {
          const price = Number(opt.metadata.replace(/[^\d.]/g, ''));

          return price >= rangeObj.min && price <= rangeObj.max;
        });
      }
    }

    if (selectedCategory && selectedCategory !== 'default') {
      filtered = filtered.filter((opt: any) => {
        return opt.title.match(new RegExp(selectedCategory, 'i'));
      });
    }

    return filtered;
  }

  // --- Helper: Build range options ---
  private getRangeOptions(allDataProducts: any[], selectedProvider: string) {
    const priceRanges = [
      { id: 'All', title: 'All', min: 0, max: Infinity },
      { id: '0-50', title: 'R0 - R50', min: 0, max: 50 },
      { id: '51-100', title: 'R51 - R100', min: 51, max: 100 },
      { id: '101-500', title: 'R101 - R500', min: 101, max: 500 },
      { id: '500+', title: 'R500+', min: 501, max: Infinity },
    ];
    const availableRangeIds = new Set();
    for (const opt of allDataProducts || []) {
      if (
        selectedProvider &&
        selectedProvider !== 'default' &&
        opt.product_provider?.toLowerCase() !== selectedProvider.toLowerCase()
      )
        continue;
      const price = Number(opt.product_value);
      for (const range of priceRanges) {
        if (price >= range.min && price <= range.max) availableRangeIds.add(range.id);
      }
    }

    return [
      { id: 'default', title: 'Select a Range', enabled: false },
      ...priceRanges.map((r) => ({
        id: r.id,
        title: r.title,
        enabled: availableRangeIds.has(r.id),
      })),
    ];
  }

  // --- Helper: Build category options ---
  private getCategoryOptions(
    allDataProducts: any[],
    selectedProvider: string,
    selectedRange: string,
  ) {
    const priceRanges = [
      { id: 'All', title: 'All', min: 0, max: Infinity },
      { id: '0-50', title: 'R0 - R50', min: 0, max: 50 },
      { id: '51-100', title: 'R51 - R100', min: 51, max: 100 },
      { id: '101-500', title: 'R101 - R500', min: 101, max: 500 },
      { id: '500+', title: 'R500+', min: 501, max: Infinity },
    ];
    const categorySource = (allDataProducts || []).filter((opt) => {
      if (
        selectedProvider &&
        selectedProvider !== 'default' &&
        opt.product_provider?.toLowerCase() !== selectedProvider.toLowerCase()
      )
        return false;
      if (selectedRange && selectedRange !== 'All' && selectedRange !== 'default') {
        const rangeObj = priceRanges.find(
          (r) => r.id === selectedRange || r.title === selectedRange,
        );
        if (!rangeObj) return false;
        const price = Number(opt.product_value);
        if (!(price >= rangeObj.min && price <= rangeObj.max)) return false;
      }

      return true;
    });
    const availableCategories = Array.from(
      new Set(
        categorySource.flatMap((opt) => {
          const match = opt.product_description.match(
            /WhatsApp|Facebook|Instagram|TikTok|YouTube|Voice|Call|Daily|Weekly|Monthly|Six Month/i,
          );

          return match ? [match[0]] : [];
        }),
      ),
    );

    return [
      { id: 'default', title: 'Select a Category', enabled: false },
      ...availableCategories.map((c) => ({ id: c, title: c })),
    ];
  }
}
