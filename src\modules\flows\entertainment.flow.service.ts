import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PP24_PROVIDERS } from 'src/util/constants';

import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { EntertainmentRequestDataDto } from './dtos/entertainment.dto';
import { SCREEN_RESPONSES } from './screenResponses/entertainment';

@Injectable()
export class EntertainmentFlowService {
  private readonly logger = new Logger(EntertainmentFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }
  private userFlows: Record<string, any> = {};

  async dataHandling(provider: string): Promise<EntertainmentRequestDataDto> {
    const requestData: EntertainmentRequestDataDto = {
      packages: [],
    };

    try {
      const pkgResponse = await this.prepaidApiService.getEntertainment(provider);

      if (pkgResponse && pkgResponse.entertainment) {
        requestData.packages = pkgResponse.entertainment.map((product) => ({
          id: product.product_code.toString(),
          title: product.product_description,
          description: product.product_provider,
          metadata: 'R' + product.product_value,
        }));
      } else {
        console.error('API Error or Invalid Response:', pkgResponse);
      }

      return requestData;
    } catch (error) {
      console.error('Data handling failed:', error);

      return requestData;
    }
  }

  async managePackages(decryptedBody: any, flowType: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // Handle ping action
    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    let requestData = data?.data || data || {}; // Ensure safe data extraction

    // Handle client error scenarios
    if (requestData?.error) {
      console.warn('Entertainment Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data && flowType != PP24_PROVIDERS.Entertainment) {
      const apiData = await this.dataHandling(flowType);
      requestData = { ...requestData, ...apiData };
    }

    // Ensure INIT always sends a valid screen
    if (action === 'INIT') {
      if (flowType != PP24_PROVIDERS.Entertainment) {
        return {
          ...SCREEN_RESPONSES.PACKAGE_VIEW,
          data: {
            ...SCREEN_RESPONSES.PACKAGE_VIEW.data,
            package_list: [...requestData.packages],
          },
        };
      }
    }

    if (!screen) {
      console.error('Error: Entertainment Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    // Ensure a user flow exists for the current session
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // Handle screen transitions
    if (action === 'data_exchange') {
      switch (screen) {
        case 'BRAND_VIEW':
          const brand = requestData.brand ?? null;
          // Match the display name from the brand
          const matchedProviderEntry = Object.entries(PP24_PROVIDERS.Entertainment).find(
            ([value]) => value == brand,
          );

          if (!matchedProviderEntry) {
            throw new Error(`Unsupported brand: ${brand}`);
          }

          const [providerKey, providerDisplayName] = matchedProviderEntry;
          const screenKey = providerKey.toUpperCase();
          const apiData = await this.dataHandling(providerDisplayName);
          requestData = { ...requestData, ...apiData };

          return {
            ...SCREEN_RESPONSES[screenKey],
            data: {
              product_list: [...requestData.packages],
            },
          };
          break;

        case 'LOTTOSTAR':
        case 'HOLLYWOOD_BETS':
        case 'BETWAY_BUCKS':
        case 'PACKAGE_VIEW':
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            package: requestData.package ?? null,
            quantity: requestData.quantity ?? 1,
            amount: requestData.amount ?? null,
          };
          const flowData = this.userFlows[flow_token];

          if (flowType == PP24_PROVIDERS.Entertainment) {
            const apiResponse = await this.dataHandling(PP24_PROVIDERS[screen]);
            requestData = { ...requestData, ...apiResponse };
          }

          let selectedPackage = requestData.packages?.find((p) => p.id === flowData.package);

          if (flowType == PP24_PROVIDERS.OTT) {
            selectedPackage = {
              id: 'OTT',
              description: 'OttVoucher',
              title: 'OTT Vouchers',
              metadata: flowData.amount,
            };
          } else if (screen == 'BETWAY_BUCKS') {
            selectedPackage = {
              id: 'BET',
              description: 'BetVoucher',
              title: 'BETWAY Vouchers',
              metadata: flowData.amount,
            };
          }

          if (!selectedPackage) {
            throw new Error(`Invalid package selection: ${flowData.package}`);
          }

          const cartItem = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Entertainment',
            provider: selectedPackage.description,
            product_code: selectedPackage.id || '',
            product_name: selectedPackage.title || 'Entertainment',
            amount: Number(selectedPackage.metadata.replace(/[^\d.]/g, '')),
            quantity: flowData.quantity,
          };
          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItem);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: {
                  flow_token,
                },
              },
            },
          };

        default:
          throw new Error('Entertainment Unhandled screen type.');
      }
    }
  }
}
