import { Injectable, NestInterceptor, ExecutionContext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, originalUrl, headers, body, query } = request;
    const startTime = Date.now();

    this.logger.debug(`Request Method: ${method}`);
    this.logger.debug(`Request URL: ${originalUrl}`);
    this.logger.debug(`Request Headers: ${JSON.stringify(headers)}`);
    this.logger.debug(`Request Body: ${JSON.stringify(body)}`);
    this.logger.debug(`Request Query Params: ${JSON.stringify(query)}`);

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        this.logger.log(`Response Time: ${endTime - startTime}ms`);
      }),
    );
  }
}
