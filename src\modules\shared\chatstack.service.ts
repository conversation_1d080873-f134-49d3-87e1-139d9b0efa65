import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ChatstackService {
  constructor(private configService: ConfigService) {}

  async saveToInputGroup(
    id: string,
    appKey: string,
    phone: string,
    body: object,
  ): Promise<boolean> {
    const url = `${this.configService.get('CHATSTACK_API_URL')}/v1/add/input/group/values`;

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: appKey,
      },
      body: JSON.stringify({
        name: id,
        id: id,
        values: [Object.assign({ number: phone }, body)],
      }),
    };

    return await (await fetch(url, options)).json();
  }

  async saveToVariableGroup(
    id: string,
    appKey: string,
    phone: string,
    values: object,
  ): Promise<boolean> {
    const url = `${this.configService.get('CHATSTACK_API_URL')}/v1/create/variable/group`;

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: appKey,
      },
      body: JSON.stringify({
        id: id,
        name: id,
        values: [
          {
            number: phone,
          },
          values,
        ],
      }),
    };

    await fetch(url, options);

    return true;
  }

  async retrieveFromInputGroup(
    id: string,
    appKey: string,
    phone: string,
    valueName?: string,
  ): Promise<any> {
    const url = `${this.configService.get('CHATSTACK_API_URL')}/v1/retrieve/input/group/contacts/values`;

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: appKey,
      },
      body: JSON.stringify({
        id: id,
        user: phone,
      }),
    };

    let response = await (await fetch(url, options)).json();

    response = response?.inputGroups;

    const obj: any = {};

    if (valueName) {
      response = response?.find((value: any) => value.name == valueName)?.value;
    } else {
      response = response?.map((value: any) => {
        obj[value.name] = value.value;
      });

      response = obj;
    }

    return response;
  }
  async retrieveFromVariableGroup(
    id: string,
    appKey: string,
    phone: string,
    valueName?: string,
  ): Promise<any> {
    const url = `${this.configService.get('CHATSTACK_API_URL')}/v1/retrieve/variable/group/contacts/values`;

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: appKey,
      },
      body: JSON.stringify({
        id: id,
        user: phone,
      }),
    };

    let response = await (await fetch(url, options)).json();

    response = response?.variables;

    const obj: any = {};

    if (valueName) {
      response = response?.find((value: any) => value.name == valueName)?.value;
    } else {
      response = response?.map((value: any) => {
        obj[value.name] = value.value;
      });

      response = obj;
    }

    return response;
  }

  async addToCart(
    inputGroupId: string,
    appKey: string,
    phone: string,
    cartItem: object,
  ): Promise<any> {
    try {
      const inputGroupValues = await this.retrieveFromInputGroup(inputGroupId, appKey, phone);

      const existingCart = inputGroupValues?.cart_details ?? [];

      if (Array.isArray(cartItem)) {
        existingCart.push(...cartItem);
      } else {
        existingCart.push(cartItem);
      }

      await this.saveToInputGroup(inputGroupId, appKey, phone, {
        cart_details: existingCart,
      });

      return true;
    } catch (error) {
      console.error('Failed to save cart details:', error);

      return false;
    }
  }

  /**
   * Trigger a specific step in the journey
   * @param phone User's phone number
   * @param appKey Application key for authentication
   * @param step Step object to trigger
   * @param journeyId Journey ID
   */
  async triggerStep(
    phone: string,
    appKey: string,
    step: object,
    journeyId: string,
  ): Promise<boolean> {
    const url = `${this.configService.get('CHATSOONER_URL')}/run/step`;

    // Ensure phone number is in the correct format
    const formattedPhone = phone.replace(/\+/g, '').replace(/^27/, '0');

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        authorization: appKey,
        number: formattedPhone,
        step,
        journeyId: journeyId,
      }),
    };

    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        console.error(`❌ Failed to trigger step: ${response.status} ${response.statusText}`);
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Failed to trigger step:', step, error);
      return false;
    }
  }
}
