# Stage 1: Build the application
FROM node:20-slim as builder

WORKDIR /usr/src/app

RUN corepack enable && corepack prepare yarn@3.6.4 --activate

COPY package.json yarn.lock ./

RUN yarn config set nodeLinker node-modules

RUN yarn install --frozen-lockfile

COPY . .

RUN ls -al

RUN yarn build

# Stage 2: Create a lightweight production image
FROM node:20-slim

WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/package.json ./package.json
COPY --from=builder /usr/src/app/node_modules ./node_modules

RUN ls -al

CMD ["npm", "run", "start:prod"]
