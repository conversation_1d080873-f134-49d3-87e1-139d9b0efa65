// src/encryption/encryption-secret.ts
import * as crypto from 'crypto';
import * as fs from 'fs';

export const generateAppSecret = () => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.randomBytes(32);
  const iv = crypto.randomBytes(16);

  const APP_SECRET = process.env.APP_SECRET;

  if (!APP_SECRET) {
    throw new Error('APP_SECRET is not set in the .env file');
  }

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(APP_SECRET, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  fs.writeFileSync(
    '.encrypted_secret',
    `${encrypted}:${key.toString('hex')}:${iv.toString('hex')}`,
  );
};
