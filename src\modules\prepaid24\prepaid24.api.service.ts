import { HttpService } from '@nestjs/axios';
import { Injectable, HttpException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';

import { DigitalDto } from '../flows/dtos/netflix.dto';
import { MeterDto } from '../flows/dtos/prepaid.dto';

@Injectable()
export class PrepaidApiService {
  private readonly logger = new Logger(PrepaidApiService.name);
  private baseUrl: string;
  private merchant: string;
  private password: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('PREPAID_API_BASE_URL');
    this.merchant = this.configService.get('PREPAID_API_MERCHANT');
    this.password = this.configService.get('PREPAID_API_PASSWORD');
    this.logger.debug(`Loaded API URL: ${this.baseUrl}`);
  }

  private getAuthPayload(): Record<string, string> {
    return { merchant: this.merchant, password: this.password };
  }

  async getServices(): Promise<any> {
    const url = `${this.baseUrl}/api/service`;
    try {
      const response = await lastValueFrom(this.httpService.post(url, this.getAuthPayload()));

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching services failed');
    }
  }

  async getClientDetails(cellNumber: string): Promise<any> {
    const url = `${this.baseUrl}/api/details`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching client details failed');
    }
  }

  // prepaid24.api.service.ts
  async getMeters(cellNumber: string): Promise<{
    result: string;
    cellnumber: string;
    meters: MeterDto[]; // Use your MeterDto here.
  }> {
    const url = `${this.baseUrl}/api/meters`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching meters failed');

      return { result: 'Failed', cellnumber: cellNumber, meters: [] }; // Return a default value in case of an error
    }
  }

  async getDstvAccounts(cellNumber: string): Promise<{
    result: string;
    cellnumber: string;
    dstv: { account: string; description: string }[];
  }> {
    const url = `${this.baseUrl}/api/dstv`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching DSTV accounts failed');

      return { result: 'Failed', cellnumber: cellNumber, dstv: [] };
    }
  }

  async getDigital(provider: string = 'all'): Promise<{
    result: string;
    digital: DigitalDto[];
  }> {
    const url = `${this.baseUrl}/api/digital`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          provider,
        }),
      );
      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching digital products failed');

      return { result: 'Failed', digital: [] };
    }
  }

  async getBills(cellNumber: string): Promise<any> {
    const url = `${this.baseUrl}/api/bills`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching bills failed');
    }
  }

  async getBalanceMeter(cellNumber: string, meter: string): Promise<any> {
    const url = `${this.baseUrl}/api/balance_meter`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
          meter,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching balance meter failed');
    }
  }

  async getMunicipalProviders(type: string): Promise<any> {
    const url = `${this.baseUrl}/api/municipal`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          provider: type,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching municipal providers failed');
    }
  }

  async processPayment(provider: string): Promise<any> {
    const url = `${this.baseUrl}/api/payment`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, { ...this.getAuthPayload(), provider }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Processing payment failed');
    }
  }

  async checkBalance(cellNumber: string, account: string): Promise<any> {
    const url = `${this.baseUrl}/api/balance_account`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
          account,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Checking balance failed');
    }
  }

  async checkCart(reference: string): Promise<any> {
    const url = `${this.baseUrl}/api/checkcart`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, { ...this.getAuthPayload(), reference }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Checking cart failed');
    }
  }

  async getReference(cellNumber: string): Promise<any> {
    const url = `${this.baseUrl}/api/reference`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          cellnumber: cellNumber,
        }),
      );
      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'error while creating reference');
    }
  }

  async getCellNumbers(cellnumber: string): Promise<any> {
    const url = `${this.baseUrl}/api/cellnumbers`;
    try {
      const payload = { ...this.getAuthPayload(), cellnumber };
      const response = await lastValueFrom(this.httpService.post(url, payload));
      this.logger.log(`${url} response: ${JSON.stringify(response.data)}`);

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching cell numbers failed');
    }
  }

  async getDataProducts(provider: string): Promise<any> {
    const url = `${this.baseUrl}/api/data`;
    try {
      const payload = { ...this.getAuthPayload(), provider };
      const response = await lastValueFrom(this.httpService.post(url, payload));
      this.logger.log(`${url} response: ${JSON.stringify(response.data)}`);

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching data products failed');
    }
  }

  async getSmsProducts(provider: string): Promise<any> {
    const url = `${this.baseUrl}/api/sms`;
    try {
      const payload = { ...this.getAuthPayload(), provider };
      const response = await lastValueFrom(this.httpService.post(url, payload));
      this.logger.log(`${url} response: ${JSON.stringify(response.data)}`);

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching SMS products failed');
    }
  }

  async createCart(cart: object): Promise<any> {
    const url = `${this.baseUrl}/api/createcart`;
    try {
      const payload = {
        ...this.getAuthPayload(),
        ...cart,
      };
      this.logger.log(`${url} request payload: ` + JSON.stringify(payload));
      const response = await lastValueFrom(this.httpService.post(url, payload));

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'error while creating cart');
    }
  }

  async getEntertainment(provider: string = 'all'): Promise<any> {
    const url = `${this.baseUrl}/api/entertainment`;
    try {
      const response = await lastValueFrom(
        this.httpService.post(url, {
          ...this.getAuthPayload(),
          provider,
        }),
      );

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'Fetching entertainment failed');
    }
  }

  /**
   * Register a new user in the system
   * @param username User's full name (min 3 characters)
   * @param usercell User's cell number (10 digits starting with 0)
   * @param usermail User's email address (valid email format)
   * @param userpass User's password (min 6 characters)
   * @returns Registration response
   */
  async registerUser(
    username: string,
    usercell: string,
    usermail: string,
    userpass: string,
  ): Promise<any> {
    const url = `${this.baseUrl}/api/reguser`;
    try {
      const payload = {
        ...this.getAuthPayload(),
        username,
        usercell,
        usermail,
        userpass,
      };

      this.logger.log(`Registering user: ${username}, ${usercell}, ${usermail}`);

      const response = await lastValueFrom(this.httpService.post(url, payload));

      this.logger.log(`${url} response: ` + JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      this.handleError(error, 'User registration failed');
    }
  }

  private handleError(error: any, message: string) {
    const status = error?.response?.status || 500;
    const errorMessage = error?.response?.data?.message || error?.message || 'Unknown error';
    this.logger.error(`❌ ${message}`, { status, errorMessage });
    throw new HttpException(errorMessage, status);
  }
}
