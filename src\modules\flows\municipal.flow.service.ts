import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SCREEN_RESPONSES } from './screenResponses/municipal';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { MunicipalBillRequestDataDto } from './dtos/municipal-bill-payment.dto';
import { ChatstackService } from '../shared/chatstack.service';

@Injectable()
export class MunicipalFlowService {
  private readonly logger = new Logger(MunicipalFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }
  private userFlows: Record<string, any> = {};

  async dataHandling(cellNumber: string): Promise<MunicipalBillRequestDataDto> {
    const requestData: MunicipalBillRequestDataDto = {
      bills: [],
    };

    try {
      const billResponse = await this.prepaidApiService.getBills(cellNumber);

      if (billResponse && billResponse.bills) {
        requestData.bills = billResponse.bills.map((bill) => ({
          id: bill.account,
          title: bill.account,
          description: bill.description,
          metadata: bill.municipality,
        }));
      } else {
        console.error('API Error or Invalid Response:', billResponse);
      }

      return requestData;
    } catch (error) {
      console.error('Data handling failed:', error);

      return requestData;
    }
  }

  async getNextScreen(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // Handle ping action
    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    let requestData = data?.data || data || {};

    // Handle client error scenarios
    if (requestData?.error) {
      console.warn('Municipal Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      const apiData = await this.dataHandling(inputGroupValues.cellnumber);
      requestData = { ...requestData, ...apiData };
    }

    // Ensure INIT always sends a valid screen
    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.MUNICIPAL,
        data: {
          ...SCREEN_RESPONSES.MUNICIPAL.data,
          municipality_list: [
            ...SCREEN_RESPONSES.MUNICIPAL.data.municipality_list,
            ...requestData.bills,
            { id: 'add_new', title: 'Add New Bill Number', description: '' },
          ],
        },
      };
    }

    if (!screen) {
      console.error('Error: Municipal Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    // Ensure a user flow exists for the current session
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // Handle screen transitions
    if (action === 'data_exchange') {
      switch (screen) {
        case 'MUNICIPAL':
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            municipality: requestData.municipality ?? null,
            amount: requestData.amount ?? null,
            easyPayNumber: requestData.municipality ?? null,
          };
          const flowState = this.userFlows[flow_token];

          const selectedProduct = requestData.bills?.find((p) => p.id === flowState.municipality);

          if (!selectedProduct) {
            throw new Error(`Invalid product selection: ${flowState.municipality}`);
          }

          const cartItem = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Bill Payment',
            provider: selectedProduct.metadata,
            number: flowState.easyPayNumber,
            easyPay_number: flowState.easyPayNumber,
            product_code: selectedProduct.id || '',
            product_name: this.formatProductName(selectedProduct.description || 'Bill Payment'),
            amount: Number(flowState.amount),
          };
          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItem);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: {
                  flow_token,
                },
              },
            },
          };

        default:
          throw new Error('Municipal Unhandled screen type.');
      }
    }
  }

  private formatProductName(description: string): string {
    return description.replace(/Municipality$/i, '').trim();
  }
}
