import { Body, Controller, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { VerifiedInterceptor } from 'src/Interceptors/verified.interceptor';

import { Prepaid24Service } from './prepaid24.service';
import { BodyDto, BotData } from '../shared/dtos/body-request.dto';
import { SessionGuard } from '../shared/guards/session.guard';

@UseGuards(SessionGuard)
@UseInterceptors(VerifiedInterceptor)
@Controller('prepaid24')
export class Prepaid24Controller {
  constructor(private prepaidSvc: Prepaid24Service) {}

  @Post('/verify-number')
  async verifyNumber(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    return await this.prepaidSvc.verifyNumber(botData);
  }

  @Post('/verify-user')
  async verifyUser(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    return await this.prepaidSvc.verifyUser(botData);
  }

  @Post('/menu')
  async handleMenu(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    return await this.prepaidSvc.handleMenu(botData);
  }

  @Post('/cart-details')
  async cartDetails(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    return await this.prepaidSvc.cartDetails(botData);
  }

  @Post('/cart-checkout')
  async cartCheckout(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    return await this.prepaidSvc.cartCheckout(botData);
  }

  @Post('/flows/validate-response')
  async validateFlowResponse(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    return await this.prepaidSvc.validateFlowResponse(botData);
  }

  @Post('/ozow/token')
  async getOzowToken(@Body() body: BodyDto): Promise<unknown> {
    const botData = new BotData(body);

    try {
      return await this.prepaidSvc.getOzowToken(botData);
    } catch (error) {
      return error;
    }
  }
}
