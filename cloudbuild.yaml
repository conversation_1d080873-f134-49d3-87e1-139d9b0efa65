steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/custom-steps-prepaid-24:$SHORT_SHA', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/custom-steps-prepaid-24:$SHORT_SHA']
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'custom-steps-prepaid-24'
      - '--image'
      - 'gcr.io/$PROJECT_ID/custom-steps-prepaid-24:$SHORT_SHA'
      - '--region'
      - 'europe-west1'
      - '--platform'
      - 'managed'
options:
  logging: CLOUD_LOGGING_ONLY
