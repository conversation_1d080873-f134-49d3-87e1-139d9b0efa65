import { Lo<PERSON>, <PERSON>du<PERSON> } from '@nestjs/common';

import { ConfigAppModule } from './config/config.module';
import { ArgusModule } from './modules/argus/argus.module';
import { FlowModule } from './modules/flows/flow.module';
import { Prepaid24Module } from './modules/prepaid24/prepaid24.module';
import { UtilModule } from './util/util.module';

@Module({
  imports: [ConfigAppModule, FlowModule, Prepaid24Module, UtilModule, ArgusModule],
  providers: [Logger],
})
export class AppModule {}
