export const SCREEN_RESPONSES = {
  ONE: {
    screen: 'ONE',
    data: {
      provider_options: [
        {
          id: 'default',
          title: 'Select a Provider',
        },
      ],
      sms_provider: [
        {
          id: 'default',
          title: 'Select a Provider',
        },
      ],
      range_options: [
        {
          id: 'default',
          title: 'Select a Range',
        },
      ],
      category_options: [
        {
          id: 'default',
          title: 'Select a Category',
        },
      ],
      mobile_options: [
        {
          id: 'default',
          title: 'Select a Mobile',
        },
      ],
      data_options: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
      airtime_options: [
        {
          id: 'default',
          title: 'Select an Airtime Product',
        },
      ],
      init_provider: '',
    },
  },
  AIRTIME: {
    screen: 'AIRTIME',
    data: {
      provider_options: [
        {
          id: 'default',
          title: 'Select a Provider',
        },
      ],
      mobile_options: [
        {
          id: 'default',
          title: 'Select a Mobile',
        },
      ],
      airtime_options: [
        {
          id: 'default',
          title: 'Select an Airtime Product',
        },
      ],
      service_provider: '',
      mobile_number: '',
    },
  },
  SUCCESS: {
    screen: 'SUCCESS',
    data: {
      extension_message_response: {
        params: {
          flow_token: 'REPLACE_FLOW_TOKEN',
          some_param_name: 'PASS_CUSTOM_VALUE',
        },
      },
    },
  },
};
