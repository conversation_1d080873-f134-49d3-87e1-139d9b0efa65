import { HttpService } from '@nestjs/axios';
import { Controller, Get } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

@Controller('api/v1/argus')
export class ArgusController {
  constructor(private readonly httpService: HttpService) {}

  @Get()
  async getBasicServerDetails() {
    const output: ServerInfoResponseDto = {
      serverTime: new Date(),
      publicIp: '',
      version: '1.0.1',
    };

    try {
      // Use HttpService to make the HTTP request
      const response = await firstValueFrom(
        this.httpService.get('https://api.ipify.org?format=json'),
      );
      output.publicIp = response.data.ip;
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err);
      output.publicIp = 'Error fetching IP';
    }

    return output;
  }
}

export class ServerInfoResponseDto {
  serverTime: Date;
  publicIp: string;
  version: string;
}
