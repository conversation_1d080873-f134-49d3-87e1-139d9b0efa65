import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppConfig {
  constructor(private configService: ConfigService) {}

  getHostingPort(): number {
    return parseInt(this.configService.get<string>('APP_PORT'));
  }

  getAppName(): string {
    return this.configService.get<string>('APP_NAME');
  }

  getAppEnvironment(): string {
    return 'Local';
  }
}
