import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';
import { LoggingInterceptor } from './Interceptors/logging.interceptor';
import { AppConfig } from './util/app.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe());
  app.useLogger(app.get(Logger));
  app.useGlobalInterceptors(new LoggingInterceptor());

  const appConfig = app.get(AppConfig);
  const PORT = appConfig.getHostingPort();

  await app.listen(PORT, () => {
    // eslint-disable-next-line no-console
    console.log('Server is listening on PORT: ', PORT);
  });
}

bootstrap();
