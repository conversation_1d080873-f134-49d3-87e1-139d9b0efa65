import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { SCREEN_RESPONSES } from './screenResponses/register';

/**
 * Interface for registration data
 */
interface IRegistrationData {
  language?: string;
  name?: string;
  surname?: string;
  email?: string;
  cellnumber?: string;
  municipality?: string;
  password?: string;
  terms?: string[];
}

@Injectable()
export class RegisterFlowService {
  private readonly logger = new Logger(RegisterFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  private userFlows: Record<string, IRegistrationData> = {};

  // Define success and failure steps
  private successStep = {
    id: 'register-success',
    entry: 'register-success',
    name: 'register-success',
    type: 'inputOptions',
    options: ['Back'],
    exitMap: {
      Back: 'welcome',
    },
    message:
      'You have completed the signing up process successfully! \n\nPlease stand by while we verify your details.',
    exit: 'welcome',
    inputGroupId: 'prepaid24Support',
  };

  private failStep = {
    id: 'register-failed',
    entry: 'register-failed',
    name: 'register-failed',
    options: ['Back'],
    exitMap: {
      Back: 'welcome',
    },
    message:
      'Sign-up Failed. \n\nWe were unable to create your account at this time. Please check your details and attempt to sign up again. If you continue to experience issues, please reach out to our support team.\n➡️ https://www.prepaid24.co.za/page/prepaid24-support',
    exit: 'welcome',
    inputGroupId: 'prepaid24Support',
  };

  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }

  /**
   * Get municipalities from the API
   * @param cellNumber The user's cell number
   * @returns List of municipalities
   */
  async getMunicipalities(cellNumber: string): Promise<any> {
    try {
      const billResponse = await this.prepaidApiService.getBills(cellNumber);

      if (billResponse && billResponse.bills) {
        return billResponse.bills.map((bill: any) => ({
          id: bill.account,
          title: bill.description ? `${bill.account} - ${bill.description}` : `${bill.account}`,
          description: bill.municipality,
        }));
      }

      return [];
    } catch (error) {
      this.logger.error('Failed to fetch municipalities', error);
      return [];
    }
  }

  /**
   * Get the next screen in the flow
   * @param decryptedBody The decrypted request body
   * @returns The next screen to display
   */
  async getNextScreen(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // Handle ping requests
    if (action === 'ping') {
      return { version, data: { status: 'active' } };
    }

    // Handle error acknowledgments
    let requestData = data?.data || data || {};
    if (requestData?.error) {
      return { version, data: { acknowledged: true } };
    }

    try {
      // Get user data from input group
      const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
        this.inputGroupId,
        this.appKey,
        flow_token,
      );

      // Fetch municipalities if needed
      if (decryptedBody.data) {
        const municipalities = await this.getMunicipalities(inputGroupValues.cellnumber);
        requestData = { ...requestData, municipality_list: municipalities };
      }

      // Initialize flow state if needed
      if (!this.userFlows[flow_token]) {
        this.userFlows[flow_token] = {};
      }

      // Update flow state with request data
      this.userFlows[flow_token] = {
        ...this.userFlows[flow_token],
        ...requestData,
      };

      // INIT action - always start with appropriate language screen
      if (action === 'INIT') {
        // Default to English for initial screen
        return {
          ...SCREEN_RESPONSES.REGISTER,
          data: {
            ...SCREEN_RESPONSES.REGISTER.data,
            municipality_list: requestData.municipality_list || [],
          },
        };
      }

      if (!screen) {
        return { version, data: { error: 'Invalid request: screen is missing or empty.' } };
      }

      // Handle data_exchange actions
      if (action === 'data_exchange') {
        this.logger.log(`🔍 Processing screen: "${screen}"`);
        this.logger.log(`🔍 Request data:`, requestData);

        switch (screen) {
          case 'REGISTER':
          case 'REGISTER_ENGLISH':
          case 'REGISTER_AFRIKAANS': {
            // Handle language selection
            if (requestData.trigger === 'language_selected') {
              return this.handleLanguageSelection(flow_token, requestData);
            }

            // Handle form submission
            const flowState = this.userFlows[flow_token];

            // Validate required fields
            if (!this.validateRegistrationData(requestData)) {
              return this.getValidationErrorScreen(flowState, requestData);
            }

            // Process registration
            try {
              return await this.processRegistration(
                flowState,
                requestData,
                inputGroupValues,
                flow_token,
              );
            } catch (error) {
              this.logger.error('Registration failed', error);
              return this.getRegistrationErrorScreen(flowState, requestData, flow_token);
            }
          }

          default:
            throw new Error('❌ Unhandled screen type.');
        }
      }
    } catch (error) {
      this.logger.error('Error in getNextScreen:', error);
      return {
        version,
        data: {
          error: 'An unexpected error occurred. Please try again.',
        },
      };
    }
  }

  /**
   * Handle language selection
   * @param flow_token The flow token
   * @param requestData The request data
   * @returns The appropriate screen based on language
   */
  private handleLanguageSelection(flow_token: string, requestData: any): any {
    const selectedLanguage = requestData.language || 'english';
    this.userFlows[flow_token].language = selectedLanguage;

    // Get the appropriate screen response
    const screenResponse =
      selectedLanguage === 'afrikaans'
        ? SCREEN_RESPONSES.REGISTER_AFRIKAANS
        : SCREEN_RESPONSES.REGISTER;

    return {
      ...screenResponse,
      data: {
        ...screenResponse.data,
        municipality_list: requestData.municipality_list || [],
      },
    };
  }

  /**
   * Get validation error screen
   * @param flowState The current flow state
   * @param requestData The request data
   * @returns The error screen
   */
  private getValidationErrorScreen(flowState: IRegistrationData, requestData: any): any {
    const selectedLanguage = flowState.language || 'english';

    // Add error message based on language
    const errorMessage =
      selectedLanguage === 'afrikaans'
        ? 'Vul asseblief alle vereiste velde in en aanvaar die terme en voorwaardes.'
        : 'Please fill in all required fields and accept the terms and conditions.';

    // Get the appropriate screen response
    const screenResponse =
      selectedLanguage === 'afrikaans'
        ? SCREEN_RESPONSES.REGISTER_AFRIKAANS
        : SCREEN_RESPONSES.REGISTER;

    return {
      ...screenResponse,
      data: {
        ...screenResponse.data,
        municipality_list: requestData.municipality_list || [],
        error_message: errorMessage,
      },
    };
  }

  /**
   * Process user registration
   * @param flowState The current flow state
   * @param requestData The request data
   * @param inputGroupValues Values from the input group
   * @param flow_token The flow token
   * @returns The success or error screen
   */
  private async processRegistration(
    flowState: IRegistrationData,
    requestData: any,
    inputGroupValues: any,
    flow_token: string,
  ): Promise<any> {
    const selectedLanguage = flowState.language || 'english';

    // Prepare user data for registration
    const fullName = `${requestData.name} ${requestData.surname}`;
    const cellNumber = inputGroupValues?.cellnumber || requestData.cellnumber || '';
    const email = requestData.email;
    const password = requestData.password;

    // Call the API to register the user
    const registrationResult = await this.prepaidApiService.registerUser(
      fullName,
      cellNumber,
      email,
      password,
    );

    this.logger.log('Registration result:', registrationResult);

    // Check if registration was successful
    if (registrationResult.result === 'Success') {
      // Save user ID to input group for future use
      await this.chatstackSvc.saveToInputGroup(this.inputGroupId, this.appKey, flow_token, {
        uid: registrationResult.uid,
        registered: true,
      });

      // Trigger the success step
      const stepTriggered = await this.chatstackSvc.triggerStep(
        flow_token,
        this.appKey,
        this.successStep,
        'Welcome',
      );

      if (!stepTriggered) {
        this.logger.warn('Failed to trigger success step for registration');
      }

      // Return success screen with appropriate message
      const successMessage =
        selectedLanguage === 'afrikaans'
          ? 'Registrasie suksesvol! Gaan asseblief jou e-pos na om jou rekening te verifieer.'
          : 'Registration successful! Please check your email to verify your account.';

      return {
        ...(selectedLanguage === 'afrikaans'
          ? SCREEN_RESPONSES.SUCCESS_AFRIKAANS
          : SCREEN_RESPONSES.SUCCESS),
        data: {
          ...(selectedLanguage === 'afrikaans'
            ? SCREEN_RESPONSES.SUCCESS_AFRIKAANS.data
            : SCREEN_RESPONSES.SUCCESS.data),
          message: successMessage,
          extension_message_response: {
            ...(selectedLanguage === 'afrikaans'
              ? SCREEN_RESPONSES.SUCCESS_AFRIKAANS.data.extension_message_response
              : SCREEN_RESPONSES.SUCCESS.data.extension_message_response),
            params: {
              ...(selectedLanguage === 'afrikaans'
                ? SCREEN_RESPONSES.SUCCESS_AFRIKAANS.data.extension_message_response.params
                : SCREEN_RESPONSES.SUCCESS.data.extension_message_response.params),
              flow_token,
              user_email: email,
            },
          },
        },
      };
    } else {
      // Registration failed with a specific reason
      const errorMessage =
        registrationResult.reason ||
        (selectedLanguage === 'afrikaans'
          ? 'Registrasie het misluk. Probeer asseblief weer.'
          : 'Registration failed. Please try again.');

      // Trigger the fail step
      const stepTriggered = await this.chatstackSvc.triggerStep(
        flow_token,
        this.appKey,
        this.failStep,
        'Welcome',
      );

      if (!stepTriggered) {
        this.logger.warn('Failed to trigger failure step for registration');
      }

      // Get the appropriate screen response
      const screenResponse =
        selectedLanguage === 'afrikaans'
          ? SCREEN_RESPONSES.REGISTER_AFRIKAANS
          : SCREEN_RESPONSES.REGISTER;

      // Return error screen
      return {
        ...screenResponse,
        data: {
          ...screenResponse.data,
          municipality_list: requestData.municipality_list || [],
          error_message: errorMessage,
        },
      };
    }
  }

  /**
   * Get registration error screen
   * @param flowState The current flow state
   * @param requestData The request data
   * @param flow_token The flow token
   * @returns The error screen
   */
  private async getRegistrationErrorScreen(
    flowState: IRegistrationData,
    requestData: any,
    flow_token: string,
  ): Promise<any> {
    const selectedLanguage = flowState.language || 'english';

    // Add error message based on language
    const errorMessage =
      selectedLanguage === 'afrikaans'
        ? 'Registrasie het misluk. Probeer asseblief weer.'
        : 'Registration failed. Please try again.';

    // Trigger the fail step
    const stepTriggered = await this.chatstackSvc.triggerStep(
      flow_token,
      this.appKey,
      this.failStep,
      'Welcome',
    );

    if (!stepTriggered) {
      this.logger.warn('Failed to trigger failure step for registration error');
    }

    // Get the appropriate screen response
    const screenResponse =
      selectedLanguage === 'afrikaans'
        ? SCREEN_RESPONSES.REGISTER_AFRIKAANS
        : SCREEN_RESPONSES.REGISTER;

    // Return the screen with error message
    return {
      ...screenResponse,
      data: {
        ...screenResponse.data,
        municipality_list: requestData.municipality_list || [],
        error_message: errorMessage,
      },
    };
  }

  /**
   * Validate registration data
   * @param data The registration data to validate
   * @returns True if valid, false otherwise
   */
  private validateRegistrationData(data: IRegistrationData): boolean {
    return !!(
      data.name &&
      data.surname &&
      data.email &&
      data.municipality &&
      data.password &&
      data.terms?.includes('yes')
    );
  }
}
