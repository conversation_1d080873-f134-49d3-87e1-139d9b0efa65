// meter.dto.ts
export class MeterDto {
  number: string;
  municipal_id: number;
  description?: string;
  municipal_name: string;
  primary_account?: string;
}

// balance-meter.dto.ts
export class BalanceMeterDto {
  sub_account: string;
  prm_account: string;
  description: string;
  amount_due: number;
  date_due: string;
}

// balance-account.dto.ts
export class BalanceAccountDto {
  sub_account: string;
  description: string;
  amount_due: number;
  date_due: string;
}

// request-data.dto.ts
export class RequestDataDto {
  meters: { id: string; title: string; description: string }[];
  prmAccounts: { id: string; title: string; description: string }[];
  electricity_arrears: string;
  rates_arrears: string;
  electricity_arrears_sub_account: string;
  rates_arrears_sub_account: string;
}
