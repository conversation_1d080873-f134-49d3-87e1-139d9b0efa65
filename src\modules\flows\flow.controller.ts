import { <PERSON>, Post, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { PrepaidJourneyService } from 'src/modules/prepaid24/prepaid24.journey.service';

@Controller('flow')
export class PrepaidFlowController {
  constructor(private readonly journeyService: PrepaidJourneyService) {}

  @Post('register')
  async handleRegister(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'register');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('prepaid')
  async handlePrepaid(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'prepaid');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('netflix')
  async handleNetflix(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'netflix');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('municipal-bill-payment')
  async handleMunicipalBillPayment(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'municipal');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('airtime-data')
  async handleAirimeandData(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'airtime-data');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('google-play')
  async handleGooglePlay(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'google-play');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('dstv')
  async handleDstv(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'dstv');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('showmax')
  async handleShowmax(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'showmax');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('ott')
  async handleOTT(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'OTT');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('blu')
  async handleBlu(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'Blu');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('uber')
  async handleUber(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'uber');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('gaming')
  async handleGaming(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'gaming');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('retail')
  async handleRetail(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'retail');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('spotify')
  async handleSpotify(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'spotify');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('microsoft')
  async handleMicrosoft(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'microsoft');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('antivirus')
  async handleAntivirus(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'antivirus');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('entertainment')
  async handleEntertainment(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'entertainment');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }

  @Post('cart/items')
  async getCartItems(@Req() req: Request, @Res() res: Response) {
    let decryptedRequest;
    try {
      decryptedRequest = await this.journeyService.handleFlows(req.body, 'cart-items');

      return res.status(200).send(decryptedRequest);
    } catch (error) {
      console.error('❌ Error:', error);

      return res.status(500).send({ message: error.message || 'Internal Server Error' });
    }
  }
}
