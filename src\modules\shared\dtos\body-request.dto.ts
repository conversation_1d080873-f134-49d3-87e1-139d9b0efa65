import { Type } from 'class-transformer';
import { IsString, ValidateNested } from 'class-validator';

class SessionDto {
  @IsString()
  userId: string;

  @IsString()
  appKey: string;

  @IsString()
  text: any;

  @ValidateNested()
  @Type(() => Object)
  session: any;
}

export class BodyDto {
  @ValidateNested()
  @Type(() => SessionDto)
  session: SessionDto;
}

export class BotData {
  currentStep: any;
  previousStep: any;
  payload: any;
  appKey: any;
  userId: any;
  contactNumber: string;
  inputGroupId: string;
  phone: string;
  text: any;

  constructor(body: BodyDto) {
    const session = body?.session;
    this.currentStep = session?.session?.currentStep;
    this.previousStep = session?.session?.previousStep;
    this.payload = session?.session?.currentStep?.endpoint?.payload;
    this.inputGroupId = this.currentStep.inputGroupId;
    this.phone = session?.userId;
    this.userId = session?.userId;
    this.appKey = session?.appKey;
    this.text = session?.text;
  }
}
