// src/keyGenerator.ts
import * as crypto from 'crypto';

const passphrase = process.argv[2];
if (!passphrase) {
  throw new Error(
    'Passphrase is empty. Please include passphrase argument like: node src/keyGenerator.ts {passphrase}',
  );
}

try {
  const keyPair = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem',
    },
    privateKeyEncoding: {
      type: 'pkcs1',
      format: 'pem',
      cipher: 'des-ede3-cbc',
      passphrase,
    },
  });

  // eslint-disable-next-line no-console
  console.debug(`Successfully created your public-private key pair:
    ************* Copy the below values *************
    PASSPHRASE="${passphrase}"
    PRIVATE_KEY="${keyPair.privateKey}"
    PUBLIC_KEY="${keyPair.publicKey}"
  `);
} catch (err) {
  console.error('Error while creating public-private key pair:', err);
}
