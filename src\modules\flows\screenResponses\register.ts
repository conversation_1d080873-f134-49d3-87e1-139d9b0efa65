export const SCREEN_RESPONSES = {
  REGISTER: {
    screen: 'REGISTER',
    data: {
      municipality_list: [],
      title: 'Register',
      language_caption: 'Language Preference',
      signup_caption: 'Sign Up',
      name_label: 'Name',
      surname_label: 'Surname',
      email_label: 'Em<PERSON>',
      municipality_label: 'Select Municipality',
      password_label: 'Set Password',
      terms_link: 'Terms & Conditions',
      accept_label: 'I Accept',
      signup_button: 'Sign Up',
    },
  },
  REGISTER_AFRIKAANS: {
    screen: 'REGISTER_AFRIKAANS',
    data: {
      municipality_list: [],
      title: '<PERSON><PERSON><PERSON>',
      language_caption: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      signup_caption: '<PERSON><PERSON>an',
      name_label: 'Na<PERSON>',
      surname_label: 'Van',
      email_label: 'E-pos',
      municipality_label: '<PERSON><PERSON> Munisipaliteit',
      password_label: 'Stel Wagwoord',
      terms_link: '<PERSON><PERSON><PERSON> & Voorwaardes',
      accept_label: '<PERSON><PERSON>',
      signup_button: '<PERSON><PERSON>',
    },
  },
  SUCCESS: {
    screen: 'SUCCESS',
    data: {
      extension_message_response: {
        params: {
          flow_token: 'REPLACE_FLOW_TOKEN',
          some_param_name: 'PASS_CUSTOM_VALUE',
        },
      },
    },
  },
  SUCCESS_AFRIKAANS: {
    screen: 'SUCCESS',
    data: {
      extension_message_response: {
        params: {
          flow_token: 'REPLACE_FLOW_TOKEN',
          some_param_name: 'PASS_CUSTOM_VALUE',
        },
      },
      message: 'Registrasie suksesvol! Gaan asseblief jou e-pos na om jou rekening te verifieer.',
    },
  },
};
