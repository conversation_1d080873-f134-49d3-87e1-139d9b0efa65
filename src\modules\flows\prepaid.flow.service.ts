import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SCREEN_RESPONSES } from './screenResponses/prepaid';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { RequestDataDto } from './dtos/prepaid.dto';
import { ChatstackService } from '../shared/chatstack.service';

/**
 * Interface for cart items
 */
interface ICartItem {
  uid: string;
  cellnumber: string;
  provider: string;
  service: string;
  number: string;
  product_code: string;
  product_name: string;
  amount: number;
}

/**
 * Interface for payment values
 */
interface IPaymentValues {
  electricity: number;
  rates: number;
  electricity_current: number;
  rates_current: number;
}

/**
 * Interface for flow state
 */
interface IFlowState {
  meter?: {
    id: string;
    title: string;
    description: string;
    primary_account?: string;
  };
  account?: {
    id: string;
    title: string;
    description: string;
  };
  electricity_arrears?: string;
  rates_arrears?: string;
  electricity_current?: string;
  rates_current?: string;
  electricity_arrears_sub_account?: string;
  rates_arrears_sub_account?: string;
  electricity_current_sub_account?: string;
  rates_current_sub_account?: string;
  electricity_arrears_amount?: number;
  rates_arrears_amount?: number;
  electricity_current_amount?: number;
  rates_current_amount?: number;
  amount?: string;
}

@Injectable()
export class PrepaidFlowService {
  private readonly logger = new Logger(PrepaidFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }
  private userFlows: Record<string, IFlowState> = {};

  async dataHandling(cellNumber: string): Promise<RequestDataDto> {
    const requestData: RequestDataDto = {
      meters: [],
      prmAccounts: [],
      electricity_arrears: '',
      rates_arrears: '',
      electricity_arrears_sub_account: '',
      rates_arrears_sub_account: '',
    };
    try {
      const metersResponse = await this.prepaidApiService.getMeters(cellNumber);

      if (metersResponse?.meters?.length) {
        // Format meters for display
        requestData.meters = metersResponse.meters
          .filter((meter) => !!meter.number)
          .map((meter) => ({
            id: meter.number,
            title: meter.description ? `${meter.number} - ${meter.description}` : `${meter.number}`,
            description: meter.municipal_name,
          }));

        // Extract and format primary accounts
        const allPrimaryAccounts = metersResponse.meters
          .map((meter) => meter.primary_account)
          .filter((account) => !!account);

        if (allPrimaryAccounts.length) {
          requestData.prmAccounts = allPrimaryAccounts.map((account) => {
            const meter = metersResponse.meters.find(
              (m: { primary_account?: string }) => m.primary_account === account,
            );

            return {
              id: `${account}`,
              title: `${account}`,
              description: meter?.number ?? '',
            };
          });
        }
      }

      return requestData;
    } catch (error) {
      this.logger.error('Data handling failed:', error);
      throw error;
    }
  }

  async getNextScreen(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // Handle ping requests
    if (action === 'ping') {
      return { version, data: { status: 'active' } };
    }

    // Handle error acknowledgments
    let requestData = data?.data || data || {};
    if (requestData?.error) {
      return { version, data: { acknowledged: true } };
    }

    // Get user data from input group
    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    // Fetch meter data if needed
    if (decryptedBody.data) {
      const apiData = await this.dataHandling(inputGroupValues.cellnumber);
      requestData = { ...requestData, ...apiData };
    }

    // Initialize flow state if needed
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // Get meter data
    const metersResponse = await this.prepaidApiService.getMeters(inputGroupValues.cellnumber);

    // Find selected meter information
    const rawSelectedMeter = metersResponse.meters?.find(
      (m: { number: string }) => m.number === requestData.meter,
    );
    const selectedMeter = requestData.meters?.find(
      (m: { id: string }) => m.id === requestData.meter,
    );

    // INIT action - always start with PREPAID screen
    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.PREPAID,
        data: {
          ...SCREEN_RESPONSES.PREPAID.data,
          meter_list: [
            ...SCREEN_RESPONSES.PREPAID.data.meter_list,
            ...requestData.meters,
            { id: 'add_new', title: 'Add New Meter' },
          ],
        },
      };
    }

    if (!screen) {
      return { version, data: { error: 'Invalid request: screen is missing or empty.' } };
    }

    if (action === 'data_exchange') {
      switch (screen) {
        case 'PREPAID': {
          // Save selected meter to flow state with primary account from raw API data
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            meter: {
              ...selectedMeter,
              primary_account: rawSelectedMeter?.primary_account,
            },
          };

          // Check if this is a Steve Tshwete meter (has primary account)
          if (rawSelectedMeter?.primary_account) {
            // Find the matching account in prmAccounts
            const matchingAccount = requestData.prmAccounts?.find(
              (acc: { id: string }) => acc.id === rawSelectedMeter.primary_account,
            );

            if (matchingAccount) {
              // Save account to flow state
              this.userFlows[flow_token] = {
                ...this.userFlows[flow_token],
                account: matchingAccount,
              };

              // Get balance information for this account
              const balanceAccountResponse = await this.prepaidApiService.checkBalance(
                inputGroupValues.cellnumber,
                matchingAccount.id,
              );

              if (balanceAccountResponse?.balance_account) {
                this.processAccountBalances(balanceAccountResponse.balance_account, requestData);
                this.updateFlowStateWithArrears(flow_token, requestData);
              }

              // Set init values based on whether there are amounts due
              const initElectricity =
                requestData.electricity_arrears && Number(requestData.electricity_arrears) > 0
                  ? ''
                  : '0';
              const initRates =
                requestData.rates_arrears && Number(requestData.rates_arrears) > 0 ? '' : '0';

              // Always go to PAY_ARREARS for Steve Tshwete meters
              return {
                ...SCREEN_RESPONSES.PAY_ARREARS,
                data: {
                  ...SCREEN_RESPONSES.PAY_ARREARS.data,
                  meter: selectedMeter.id,
                  electricity_arrears: `Electricity Arrears - ${requestData.electricity_arrears_sub_account || ''} - R${requestData.electricity_arrears || '0'}`,
                  rates_arrears: `Rates Arrears - ${requestData.rates_arrears_sub_account || ''} - R${requestData.rates_arrears || '0'}`,
                  electricity_current: `Electricity Current - ${requestData.electricity_current_sub_account || ''} - R${requestData.electricity_current || '0'}`,
                  rates_current: `Rates Current - ${requestData.rates_current_sub_account || ''} - R${requestData.rates_current || '0'}`,
                  init_electricity: initElectricity,
                  init_rates: initRates,
                },
              };
            }
          }

          // For normal meters, go directly to PURCHASE with is_optional forced to true
          return {
            ...SCREEN_RESPONSES.PURCHASE,
            data: {
              ...SCREEN_RESPONSES.PURCHASE.data,
              meter: selectedMeter?.id || '',
              meter_number: `Meter: ${selectedMeter?.id}` || '',
              is_optional: true, // Normal users always have optional purchase
            },
          };
        }

        case 'PAY_ARREARS': {
          const flowState = this.userFlows[flow_token];
          // Get entered values
          const entered = {
            electricity: Number(requestData.electricity_arrears_amount || 0),
            rates: Number(requestData.rates_arrears_amount || 0),
            electricity_current: Number(requestData.electricity_current_amount || 0),
            rates_current: Number(requestData.rates_current_amount || 0),
          };

          // Get expected values
          const expected = {
            electricity: Number(flowState.electricity_arrears || 0),
            rates: Number(flowState.rates_arrears || 0),
            electricity_current: Number(flowState.electricity_current || 0),
            rates_current: Number(flowState.rates_current || 0),
          };

          // Helper checks
          const allEnteredZero =
            entered.electricity === 0 &&
            entered.rates === 0 &&
            entered.electricity_current === 0 &&
            entered.rates_current === 0;
          const allExpectedArrearsZero = expected.electricity === 0 && expected.rates === 0;

          // 1. All arrears settled (entered == expected for arrears, expected > 0): Go to PURCHASE
          // This handles the case where arrears are fully settled, even if current payments are included
          if (this.areAllArrearsFulfilledOrZero(entered, expected)) {
            this.updateFlowStateWithAmounts(flow_token, entered);
            const paidAccounts = this.buildPaidAccountsList(entered);

            // For all meters, go to PURCHASE
            return this.getPurchaseScreen(
              flowState,
              entered.electricity.toString(),
              entered.rates.toString(),
              entered.electricity_current.toString(),
              entered.rates_current.toString(),
              paidAccounts.join('\n') || '',
            );
          }

          // 2. All entered zero, all arrears expected 0: Go to PURCHASE
          if (allEnteredZero && allExpectedArrearsZero) {
            this.updateFlowStateWithZeroAmounts(flow_token);

            // For Steve Tshwete meters, we still need to go to PURCHASE to allow electricity purchase
            return this.getPurchaseScreen(flowState, '0', '0', '0', '0', '');
          }

          // 3. All entered zero, any arrears expected >0: Reload PAY_ARREARS with error
          if (allEnteredZero && (expected.electricity > 0 || expected.rates > 0)) {
            return this.getPayArrearsScreenWithError(
              flowState,
              'You must pay at least a partial amount towards an arrears account.',
            );
          }

          // 4. Partial/overpayment of arrears (entered != expected for any arrears, and any entered > 0):
          // Always add all to cart and kick out
          if (this.hasPartialOrOverPayment(entered, expected)) {
            // Update flow state with entered amounts
            this.updateFlowStateWithAmounts(flow_token, entered);

            // Add items to cart
            await this.addItemsToCart(
              entered,
              flowState,
              inputGroupValues,
              flow_token,
              'Partial arrears payment added to cart',
            );

            // Return success screen with kicked_out flag
            return this.getSuccessScreenWithCartMessage(flow_token);
          }

          // 5. Only current payments with different handling based on arrears
          if (
            entered.electricity === 0 &&
            entered.rates === 0 &&
            (entered.electricity_current > 0 || entered.rates_current > 0)
          ) {
            // Update flow state with current amounts only
            this.updateFlowStateWithCurrentAmounts(flow_token, entered);

            // If arrears are expected, add to cart
            if (expected.electricity > 0 || expected.rates > 0) {
              // Create cart items from entered amounts
              const baseCartItem = this.createBaseCartItem(inputGroupValues, flowState);
              const cartItems = this.createCartItemsFromEnteredAmounts(
                entered,
                flowState,
                baseCartItem,
              );

              // Add items to cart if any exist
              if (cartItems.length > 0) {
                await this.chatstackSvc.addToCart(
                  this.inputGroupId,
                  this.appKey,
                  flow_token,
                  cartItems,
                );
                this.logger.debug('Current payments with outstanding arrears added to cart');
              }

              // Return success screen
              return this.getSuccessScreenWithCartMessage(flow_token);
            }
            // If no arrears are expected, go to PURCHASE
            else {
              // Build paid accounts list for display
              const paidAccounts = this.buildCurrentAccountsList(entered);

              return this.getPurchaseScreen(
                flowState,
                '0',
                '0',
                entered.electricity_current.toString(),
                entered.rates_current.toString(),
                paidAccounts.join('\n') || '',
              );
            }
          }

          // 6. Any payment for a single arrears account (when both have balances)
          // or any partial payment of an arrears account: Add to cart
          if (
            expected.electricity > 0 &&
            expected.rates > 0 &&
            ((entered.electricity > 0 && entered.rates === 0) ||
              (entered.rates > 0 && entered.electricity === 0))
          ) {
            // Update flow state with entered amounts
            this.updateFlowStateWithAmounts(flow_token, entered);

            // Add items to cart
            await this.addItemsToCart(
              entered,
              flowState,
              inputGroupValues,
              flow_token,
              'Single arrears account payment added to cart',
            );

            // Return success screen
            return this.getSuccessScreenWithCartMessage(flow_token);
          }

          // 7. Additional case: If we've reached this point, it means we have an unhandled case
          // Log it and show an error to the user
          this.logger.warn('Unhandled payment case detected', { entered, expected });

          return this.getPayArrearsScreenWithError(
            flowState,
            'Invalid payment combination. Please try again.',
          );
        }

        case 'PURCHASE': {
          const flowState = this.userFlows[flow_token];
          // Save amount to flow state
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            amount: requestData.amount || '0',
          };

          try {
            const baseCartItem = this.createBaseCartItem(inputGroupValues, flowState);

            const cartItems = [];

            // Create descriptive product names
            const meterNumber = flowState.meter?.id || '';

            // For Electricity: use the meter title directly (already formatted correctly)
            const meterTitle = flowState.meter?.title || meterNumber;
            const electricityProductName = `${meterTitle}`;

            // Format for other items: use the sub-account code format without leading hyphen
            const electricityArrearsProductName = `${flowState.electricity_arrears_sub_account || ''} - Electricity Arrears`;
            const ratesArrearsProductName = `${flowState.rates_arrears_sub_account || ''} - Rates Arrears`;
            const electricityCurrentProductName = `${flowState.electricity_current_sub_account || ''} - Electricity Current`;
            const ratesCurrentProductName = `${flowState.rates_current_sub_account || ''} - Rates Current`;

            // For Steve Tshwete meters with arrears
            if (flowState.meter?.primary_account) {
              // Add arrears and current payments to cart if amounts are entered
              if (
                flowState.electricity_arrears_amount &&
                Number(flowState.electricity_arrears_amount) > 0
              ) {
                cartItems.push({
                  ...baseCartItem,
                  product_code: flowState.electricity_arrears_sub_account || '',
                  product_name: electricityArrearsProductName,
                  amount: Number(flowState.electricity_arrears_amount),
                });
              }

              if (flowState.rates_arrears_amount && Number(flowState.rates_arrears_amount) > 0) {
                cartItems.push({
                  ...baseCartItem,
                  product_code: flowState.rates_arrears_sub_account || '',
                  product_name: ratesArrearsProductName,
                  amount: Number(flowState.rates_arrears_amount),
                });
              }

              if (
                flowState.electricity_current_amount &&
                Number(flowState.electricity_current_amount) > 0
              ) {
                cartItems.push({
                  ...baseCartItem,
                  product_code: flowState.electricity_current_sub_account || '',
                  product_name: electricityCurrentProductName,
                  amount: Number(flowState.electricity_current_amount),
                });
              }

              if (flowState.rates_current_amount && Number(flowState.rates_current_amount) > 0) {
                cartItems.push({
                  ...baseCartItem,
                  product_code: flowState.rates_current_sub_account || '',
                  product_name: ratesCurrentProductName,
                  amount: Number(flowState.rates_current_amount),
                });
              }
            }

            // For ALL meters (both Steve Tshwete and normal), add electricity purchase if amount is entered
            if (requestData.amount && Number(requestData.amount) > 0) {
              cartItems.push({
                ...baseCartItem,
                number: flowState.meter?.id || '',
                product_code: flowState.meter?.id || '',
                product_name: electricityProductName,
                amount: Number(requestData.amount),
              });
            }

            // Add items to cart if any exist
            if (cartItems.length > 0) {
              await this.chatstackSvc.addToCart(
                this.inputGroupId,
                this.appKey,
                flow_token,
                cartItems,
              );
              this.logger.debug('Items added to cart successfully');
            } else {
              this.logger.warn('No items to add to cart');
            }

            // Go to SUCCESS screen
            return {
              ...SCREEN_RESPONSES.SUCCESS,
              data: {
                ...SCREEN_RESPONSES.SUCCESS.data,
                extension_message_response: {
                  params: { flow_token },
                },
              },
            };
          } catch (error) {
            this.logger.error('Error adding items to cart:', error);

            return this.getPurchaseScreenWithError(
              flowState,
              'Failed to add items to cart. Please try again.',
            );
          }
        }

        default:
          throw new Error('❌ Unhandled screen type.');
      }
    }
  }

  /**
   * Process account balances from API response
   * @param balanceAccounts The balance accounts from API response
   * @param requestData The request data to update
   */
  private processAccountBalances(balanceAccounts: any[], requestData: any): void {
    balanceAccounts.forEach((account: any) => {
      if (account.description === 'Electricity Arrears') {
        requestData.electricity_arrears = account.amount_due.toString();
        requestData.electricity_arrears_sub_account = account.sub_account;
      }

      if (account.description === 'Rates Arrears') {
        requestData.rates_arrears = account.amount_due.toString();
        requestData.rates_arrears_sub_account = account.sub_account;
      }

      if (account.description === 'Electricity Current') {
        requestData.electricity_current = account.amount_due.toString();
        requestData.electricity_current_sub_account = account.sub_account;
      }

      if (account.description === 'Rates Current') {
        requestData.rates_current = account.amount_due.toString();
        requestData.rates_current_sub_account = account.sub_account;
      }
    });
  }

  /**
   * Update flow state with arrears data
   * @param flowToken The flow token
   * @param requestData The request data with arrears information
   */
  private updateFlowStateWithArrears(flowToken: string, requestData: any): void {
    this.userFlows[flowToken] = {
      ...this.userFlows[flowToken],
      electricity_arrears: requestData.electricity_arrears || '0',
      rates_arrears: requestData.rates_arrears || '0',
      electricity_current: requestData.electricity_current || '0',
      rates_current: requestData.rates_current || '0',
      electricity_arrears_sub_account: requestData.electricity_arrears_sub_account || '',
      rates_arrears_sub_account: requestData.rates_arrears_sub_account || '',
      electricity_current_sub_account: requestData.electricity_current_sub_account || '',
      rates_current_sub_account: requestData.rates_current_sub_account || '',
    };
  }

  /**
   * Check if all arrears are fully settled (entered == expected for arrears)
   * @param entered The entered payment values
   * @param expected The expected payment values
   * @returns True if all arrears are fully settled
   */
  private areAllArrearsFulfilledOrZero(entered: IPaymentValues, expected: IPaymentValues): boolean {
    // Check if all expected arrears are fully settled
    const electricitySettled =
      expected.electricity === 0 || entered.electricity === expected.electricity;
    const ratesSettled = expected.rates === 0 || entered.rates === expected.rates;

    // Both arrears accounts must be fully settled
    return (
      (expected.electricity > 0 || expected.rates > 0) && // At least one arrears account has a balance
      electricitySettled &&
      ratesSettled
    );
  }

  /**
   * Update flow state with entered payment amounts
   * @param flowToken The flow token
   * @param entered The entered payment values
   */
  private updateFlowStateWithAmounts(flowToken: string, entered: IPaymentValues): void {
    this.userFlows[flowToken] = {
      ...this.userFlows[flowToken],
      electricity_arrears_amount: entered.electricity,
      rates_arrears_amount: entered.rates,
      electricity_current_amount: entered.electricity_current,
      rates_current_amount: entered.rates_current,
    };
  }

  /**
   * Build a list of paid accounts for display
   * @param entered The entered payment values
   * @returns Array of paid account strings
   */
  private buildPaidAccountsList(entered: IPaymentValues): string[] {
    const paidAccounts = [];
    if (entered.electricity > 0) {
      paidAccounts.push(`Electricity Arrears: R${entered.electricity}`);
    }

    if (entered.rates > 0) {
      paidAccounts.push(`Rates Arrears: R${entered.rates}`);
    }

    if (entered.electricity_current > 0) {
      paidAccounts.push(`Electricity Current: R${entered.electricity_current}`);
    }

    if (entered.rates_current > 0) {
      paidAccounts.push(`Rates Current: R${entered.rates_current}`);
    }

    return paidAccounts;
  }

  /**
   * Update flow state with zero amounts
   * @param flowToken The flow token
   */
  private updateFlowStateWithZeroAmounts(flowToken: string): void {
    this.userFlows[flowToken] = {
      ...this.userFlows[flowToken],
      electricity_arrears_amount: 0,
      rates_arrears_amount: 0,
      electricity_current_amount: 0,
      rates_current_amount: 0,
    };
  }

  /**
   * Get PAY_ARREARS screen with error message
   * @param flowState The current flow state
   * @param errorMessage The error message to display
   * @returns PAY_ARREARS screen with error message
   */
  private getPayArrearsScreenWithError(
    flowState: IFlowState,
    errorMessage: string,
    enteredValues?: IPaymentValues,
  ): any {
    // Set init values based on whether there are amounts due
    const initElectricity =
      flowState.electricity_arrears && Number(flowState.electricity_arrears) > 0 ? '' : '0';
    const initRates = flowState.rates_arrears && Number(flowState.rates_arrears) > 0 ? '' : '0';

    // If entered values are provided, use them to preserve user input
    const electricityArrearsAmount = enteredValues?.electricity
      ? enteredValues.electricity.toString()
      : '';
    const ratesArrearsAmount = enteredValues?.rates ? enteredValues.rates.toString() : '';
    const electricityCurrentAmount = enteredValues?.electricity_current
      ? enteredValues.electricity_current.toString()
      : '';
    const ratesCurrentAmount = enteredValues?.rates_current
      ? enteredValues.rates_current.toString()
      : '';

    return {
      ...SCREEN_RESPONSES.PAY_ARREARS,
      data: {
        ...SCREEN_RESPONSES.PAY_ARREARS.data,
        meter: flowState.meter?.id || '',
        electricity_arrears: `Electricity Arrears - ${flowState.electricity_arrears_sub_account || ''} - R${flowState.electricity_arrears || '0'}`,
        rates_arrears: `Rates Arrears - ${flowState.rates_arrears_sub_account || ''} - R${flowState.rates_arrears || '0'}`,
        electricity_current: `Electricity Current - ${flowState.electricity_current_sub_account || ''} - R${flowState.electricity_current || '0'}`,
        rates_current: `Rates Current - ${flowState.rates_current_sub_account || ''} - R${flowState.rates_current || '0'}`,
        error_message: errorMessage,
        init_electricity: initElectricity,
        init_rates: initRates,
        // Preserve user input if provided
        electricity_arrears_amount: electricityArrearsAmount,
        rates_arrears_amount: ratesArrearsAmount,
        electricity_current_amount: electricityCurrentAmount,
        rates_current_amount: ratesCurrentAmount,
      },
    };
  }

  /**
   * Check if there is a partial or over payment of arrears
   * @param entered The entered payment values
   * @param expected The expected payment values
   * @returns True if there is a partial or over payment of arrears
   */
  private hasPartialOrOverPayment(entered: IPaymentValues, expected: IPaymentValues): boolean {
    // If any arrears are expected and the entered amount doesn't match exactly, it's a partial payment
    // Also consider it a partial payment if only one of the arrears accounts is being paid
    return (
      // Electricity arrears partial payment
      (expected.electricity > 0 &&
        entered.electricity > 0 &&
        entered.electricity !== expected.electricity) ||
      // Rates arrears partial payment
      (expected.rates > 0 && entered.rates > 0 && entered.rates !== expected.rates) ||
      // Only paying one arrears account when both have balances
      (expected.electricity > 0 &&
        expected.rates > 0 &&
        ((entered.electricity > 0 && entered.rates === 0) ||
          (entered.rates > 0 && entered.electricity === 0)))
    );
  }

  /**
   * Create base cart item from input group values and flow state
   * @param inputGroupValues The input group values
   * @param flowState The current flow state
   * @returns Base cart item
   */
  private createBaseCartItem(inputGroupValues: any, flowState: IFlowState): any {
    return {
      uid: inputGroupValues.userDetails?.uid,
      cellnumber: inputGroupValues.userDetails?.cellnumber || inputGroupValues.cellnumber,
      provider: flowState.meter?.description || '',
      service: 'Electricity',
      number: flowState.account?.id || flowState.meter?.primary_account || '',
    };
  }

  /**
   * Create cart items from entered amounts
   * @param entered The entered payment values
   * @param flowState The current flow state
   * @param baseCartItem The base cart item
   * @returns Array of cart items
   */
  private createCartItemsFromEnteredAmounts(
    entered: IPaymentValues,
    flowState: IFlowState,
    baseCartItem: any,
  ): ICartItem[] {
    const cartItems: ICartItem[] = [];

    // Format for other items: use the sub-account code format without leading hyphen
    const electricityArrearsProductName = `${flowState.electricity_arrears_sub_account || ''} -Electricity Arrears`;
    const ratesArrearsProductName = `${flowState.rates_arrears_sub_account || ''} - Rates Arrears`;
    const electricityCurrentProductName = `${flowState.electricity_current_sub_account || ''} - Electricity Current`;
    const ratesCurrentProductName = `${flowState.rates_current_sub_account || ''} - Rates Current`;

    if (entered.electricity > 0) {
      cartItems.push({
        ...baseCartItem,
        product_code: flowState.electricity_arrears_sub_account || '',
        product_name: electricityArrearsProductName,
        amount: entered.electricity,
      });
    }

    if (entered.rates > 0) {
      cartItems.push({
        ...baseCartItem,
        product_code: flowState.rates_arrears_sub_account || '',
        product_name: ratesArrearsProductName,
        amount: entered.rates,
      });
    }

    if (entered.electricity_current > 0) {
      cartItems.push({
        ...baseCartItem,
        product_code: flowState.electricity_current_sub_account || '',
        product_name: electricityCurrentProductName,
        amount: entered.electricity_current,
      });
    }

    if (entered.rates_current > 0) {
      cartItems.push({
        ...baseCartItem,
        product_code: flowState.rates_current_sub_account || '',
        product_name: ratesCurrentProductName,
        amount: entered.rates_current,
      });
    }

    return cartItems;
  }

  /**
   * Add items to cart based on entered amounts
   * @param entered The entered payment values
   * @param flowState The current flow state
   * @param inputGroupValues The input group values
   * @param flow_token The flow token
   * @param logMessage The message to log after adding to cart
   */
  private async addItemsToCart(
    entered: IPaymentValues,
    flowState: IFlowState,
    inputGroupValues: any,
    flow_token: string,
    logMessage: string,
  ): Promise<void> {
    // Create cart items from entered amounts
    const baseCartItem = this.createBaseCartItem(inputGroupValues, flowState);
    const cartItems = this.createCartItemsFromEnteredAmounts(entered, flowState, baseCartItem);

    // Add items to cart if any exist
    if (cartItems.length > 0) {
      await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItems);
      this.logger.debug(logMessage);
    }
  }

  /**
   * Get success screen with cart message
   * @param flow_token The flow token
   * @returns Success screen with cart message
   */
  private getSuccessScreenWithCartMessage(flow_token: string): any {
    return {
      ...SCREEN_RESPONSES.SUCCESS,
      data: {
        ...SCREEN_RESPONSES.SUCCESS.data,
        extension_message_response: {
          params: { flow_token },
        },
        kicked_out: true,
        message: 'You have been exited from the flow. Your payment has been added to the cart.',
      },
    };
  }

  /**
   * Get PURCHASE screen with standard data
   * @param flowState The current flow state
   * @param electricityArrearsAmount The electricity arrears amount
   * @param ratesArrearsAmount The rates arrears amount
   * @param electricityCurrentAmount The electricity current amount
   * @param ratesCurrentAmount The rates current amount
   * @param paidAccounts The paid accounts string
   * @returns PURCHASE screen with standard data
   */
  private getPurchaseScreen(
    flowState: IFlowState,
    electricityArrearsAmount: string,
    ratesArrearsAmount: string,
    electricityCurrentAmount: string,
    ratesCurrentAmount: string,
    paidAccounts: string,
  ): any {
    // Set is_optional based on user type and paid accounts
    const isSteveTshweteUser = !!flowState.meter?.primary_account;

    // For Steve Tshwete users, is_optional should be true if there are no paid accounts
    // Otherwise, it should be false for Steve Tshwete users and true for normal users
    const isOptional = isSteveTshweteUser
      ? paidAccounts === '' // true if no paid accounts for Steve Tshwete users
      : true; // always true for normal users

    return {
      ...SCREEN_RESPONSES.PURCHASE,
      data: {
        ...SCREEN_RESPONSES.PURCHASE.data,
        electricity_arrears_amount: electricityArrearsAmount,
        rates_arrears_amount: ratesArrearsAmount,
        electricity_current_amount: electricityCurrentAmount,
        rates_current_amount: ratesCurrentAmount,
        meter: flowState.meter?.id || '',
        meter_number: `Meter: ${flowState.meter?.id}` || '',
        paid_accounts: paidAccounts,
        is_optional: isOptional,
      },
    };
  }

  /**
   * Get PURCHASE screen with error message
   * @param flowState The current flow state
   * @param errorMessage The error message to display
   * @returns PURCHASE screen with error message
   */
  private getPurchaseScreenWithError(flowState: IFlowState, errorMessage: string): any {
    // For error screens, we'll default to true since we don't know if there are paid accounts
    const isOptional = true;

    return {
      ...SCREEN_RESPONSES.PURCHASE,
      data: {
        ...SCREEN_RESPONSES.PURCHASE.data,
        meter: flowState.meter?.id || '',
        meter_number: flowState.meter?.id || '',
        error: errorMessage,
        is_optional: isOptional,
      },
    };
  }

  /**
   * Update flow state with current amounts only
   * @param flowToken The flow token
   * @param entered The entered payment values
   */
  private updateFlowStateWithCurrentAmounts(flowToken: string, entered: IPaymentValues): void {
    this.userFlows[flowToken] = {
      ...this.userFlows[flowToken],
      electricity_arrears_amount: 0,
      rates_arrears_amount: 0,
      electricity_current_amount: entered.electricity_current,
      rates_current_amount: entered.rates_current,
    };
  }

  /**
   * Build a list of current accounts for display
   * @param entered The entered payment values
   * @returns Array of current account strings
   */
  private buildCurrentAccountsList(entered: IPaymentValues): string[] {
    const paidAccounts = [];
    if (entered.electricity_current > 0) {
      paidAccounts.push(`Electricity Current: R${entered.electricity_current}`);
    }

    if (entered.rates_current > 0) {
      paidAccounts.push(`Rates Current: R${entered.rates_current}`);
    }

    return paidAccounts;
  }
}
