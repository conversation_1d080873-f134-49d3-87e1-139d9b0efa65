// src/encryption/encryption.service.ts
import * as crypto from 'crypto';

import { Injectable } from '@nestjs/common';

import { FlowEndpointException } from './flow-endpoint-exception';

@Injectable()
export class EncryptionService {
  private readonly privateKey: string;
  private readonly passphrase: string;

  constructor() {
    this.privateKey = process.env.PRIVATE_KEY
      ? Buffer.from(process.env.PRIVATE_KEY, 'base64').toString('utf-8')
      : null;
    this.passphrase = process.env.PASSPHRASE;
  }

  decryptRequest(body: any): any {
    const { encrypted_aes_key, encrypted_flow_data, initial_vector } = body;

    let decryptedAesKey;
    try {
      const privateKey = crypto.createPrivateKey({
        key: this.privateKey,
        passphrase: this.passphrase,
      });

      decryptedAesKey = crypto.privateDecrypt(
        {
          key: privateKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256',
        },
        Buffer.from(encrypted_aes_key, 'base64'),
      );
    } catch (error) {
      console.error(error);
      throw new FlowEndpointException(421, 'Failed to decrypt the request.');
    }

    const flowDataBuffer = Buffer.from(encrypted_flow_data, 'base64');
    const initialVectorBuffer = Buffer.from(initial_vector, 'base64');

    const TAG_LENGTH = 16;
    const encrypted_flow_data_body = flowDataBuffer.subarray(0, -TAG_LENGTH);
    const encrypted_flow_data_tag = flowDataBuffer.subarray(-TAG_LENGTH);

    const decipher = crypto.createDecipheriv('aes-128-gcm', decryptedAesKey, initialVectorBuffer);
    decipher.setAuthTag(encrypted_flow_data_tag);

    const decryptedJSONString = Buffer.concat([
      decipher.update(encrypted_flow_data_body),
      decipher.final(),
    ]).toString('utf-8');

    return {
      decryptedBody: JSON.parse(decryptedJSONString),
      aesKeyBuffer: decryptedAesKey,
      initialVectorBuffer,
    };
  }

  encryptResponse(response: any, aesKeyBuffer: Buffer, initialVectorBuffer: Buffer): string {
    const flipped_iv = initialVectorBuffer.map((byte) => ~byte);

    const cipher = crypto.createCipheriv('aes-128-gcm', aesKeyBuffer, flipped_iv);

    return Buffer.concat([
      cipher.update(JSON.stringify(response), 'utf8'),
      cipher.final(),
      cipher.getAuthTag(),
    ]).toString('base64');
  }
}
