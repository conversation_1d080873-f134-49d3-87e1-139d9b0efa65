export const SCREEN_RESPONSES = {
  GAMING_VOUCHERS: {
    version: '3.0',
    screen: 'GAMING_VOUCHERS',
    data: {
      brand_list: [
        {
          id: 'default',
          title: 'Select a Brand',
        },
      ],
    },
  },
  PLAYSTATION: {
    version: '3.0',
    screen: 'PLAYSTATION',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  MINECOINS: {
    version: '3.0',
    screen: 'MINECOINS',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  PUBG: {
    version: '3.0',
    screen: 'PUBG',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  XBOX: {
    screen: 'XBOX',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  STEAM: {
    version: '3.0',
    screen: 'STEAM',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  ROBLOX: {
    version: '3.0',
    screen: 'ROBLOX',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  SUCCESS: {
    version: '3.0',
    screen: 'SUCCESS',
    data: {
      extension_message_response: {
        params: {
          flow_token: 'REPLACE_FLOW_TOKEN',
          some_param_name: 'PASS_CUSTOM_VALUE',
        },
      },
    },
  },
};
