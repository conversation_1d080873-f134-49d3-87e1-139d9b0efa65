import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SCREEN_RESPONSES } from './screenResponses/gaming-vouchers';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { GamingRequestDataDto } from './dtos/gaming-voucher.dto';

@Injectable()
export class GamingFlowService {
  private readonly logger = new Logger(GamingFlowService.name);
  private appKey: string;
  private inputGroupId: string;

  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }

  private userFlows: Record<string, any> = {};

  private formatBrandKey(brand: string): string {
    return brand.toUpperCase().replace(/-/g, '_').replace(/\s+/g, '_'); // Replace any spaces with underscores
  }

  async dataHandling(provider: string): Promise<GamingRequestDataDto> {
    const requestData: GamingRequestDataDto = {
      packages: [],
    };

    try {
      const digitalResponse = await this.prepaidApiService.getDigital('all');
      this.logger.debug(`Received digital response for ${provider}:`, digitalResponse);

      if (digitalResponse && digitalResponse.digital) {
        // Map provider names to their API counterparts
        const providerMapping: Record<string, string[]> = {
          PLAYSTATION: ['PlayStation'],
          MINECOINS: ['Minecraft', 'Minecoins'],
          PUBG: ['PUBG'],
          XBOX: ['Xbox'],
          STEAM: ['Steam'],
          ROBLOX: ['Roblox'],
        };

        const searchTerms = providerMapping[provider.toUpperCase()] || [provider];
        this.logger.debug(`Using search terms for ${provider}:`, searchTerms);

        requestData.packages = digitalResponse.digital
          .filter((item) => {
            const matches = searchTerms.some(
              (term) =>
                item.product_description.toLowerCase().includes(term.toLowerCase()) ||
                item.product_provider.toLowerCase().includes(term.toLowerCase()),
            );
            this.logger.debug(`Item ${item.product_description} matches: ${matches}`);

            return matches;
          })
          .map((item) => ({
            id: item.product_code?.toString() ?? '',
            title: item.product_description,
            description: item.product_provider,
            metadata: 'R' + item.product_value,
          }));

        // Sort packages by value for better presentation
        requestData.packages.sort((a, b) => {
          const valueA = Number(a.metadata.replace(/[^\d.]/g, ''));
          const valueB = Number(b.metadata.replace(/[^\d.]/g, ''));

          return valueA - valueB;
        });
      }

      return requestData;
    } catch (error) {
      this.logger.error('Data handling failed:', error);

      return requestData;
    }
  }

  async managePackages(decryptedBody: any, flowType: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;
    this.logger.debug('Received request:', { screen, action, flowType });

    if (action === 'ping') {
      return { version, data: { status: 'active' } };
    }

    const requestData = data?.data || {};

    if (requestData?.error) {
      this.logger.warn('Gaming Received client error:', requestData);

      return { version, data: { acknowledged: true } };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.GAMING_VOUCHERS,
        data: {
          ...SCREEN_RESPONSES.GAMING_VOUCHERS.data,
          brand_list: [
            { id: 'default', title: 'Select a Brand' },
            { id: 'PLAYSTATION', title: 'PlayStation' },
            { id: 'MINECOINS', title: 'Minecoins' },
            { id: 'PUBG', title: 'PUBG' },
            { id: 'XBOX', title: 'Xbox' },
            { id: 'STEAM', title: 'Steam' },
            { id: 'ROBLOX', title: 'Roblox' },
          ],
        },
      };
    }

    if (action === 'data_exchange') {
      switch (screen) {
        case 'GAMING_VOUCHERS': {
          const brand = requestData.brand || '';
          if (!brand || brand.toLowerCase() === 'default') {
            return {
              version,
              screen: 'GAMING_VOUCHERS',
              data: { error: 'Please select a valid brand' },
            };
          }

          const brandKey = this.formatBrandKey(brand);
          this.logger.debug(`Processing brand: ${brandKey}`);

          const apiData = await this.dataHandling(brandKey);

          if (!SCREEN_RESPONSES[brandKey]) {
            this.logger.error(`No screen response found for ${brandKey}`);

            return {
              version,
              screen: 'GAMING_VOUCHERS',
              data: { error: 'Invalid brand selection' },
            };
          }

          return {
            version,
            screen: brandKey,
            data: {
              brand: brand,
              product_list: [{ id: 'default', title: 'Select a Product' }, ...apiData.packages],
            },
          };
        }

        case 'PLAYSTATION':
        case 'MINECOINS':
        case 'PUBG':
        case 'XBOX':
        case 'STEAM':
        case 'ROBLOX': {
          const product = requestData.products;
          if (!product || product === 'default') {
            return {
              version,
              data: { error: 'Please select a valid product' },
            };
          }

          const apiData = await this.dataHandling(screen);
          const selectedProduct = apiData.packages.find((p) => p.id === product);

          if (!selectedProduct) {
            return {
              version,
              data: { error: 'Invalid product selection' },
            };
          }

          const quantity = Number(requestData.quantity) || 1;
          const cartItems = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Digital',
            provider: selectedProduct.description,
            product_code: selectedProduct.id,
            product_name: selectedProduct.title,
            amount: Number(selectedProduct.metadata.replace(/[^\d.]/g, '')),
            quantity,
          };

          this.logger.debug('🧺 Final cart items:', JSON.stringify(cartItems, null, 2));
          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItems);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: { flow_token },
              },
            },
          };
        }

        default:
          this.logger.error(`Unhandled screen type: ${screen}`);

          return {
            version,
            data: { error: `Unhandled screen type: ${screen}` },
          };
      }
    }

    this.logger.warn(`Unhandled action type: ${action}`);

    return {
      version,
      data: { error: `Unhandled action type: ${action}` },
    };
  }
}
