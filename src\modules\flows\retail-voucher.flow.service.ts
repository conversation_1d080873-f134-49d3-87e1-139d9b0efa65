import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SCREEN_RESPONSES } from './screenResponses/retail-vouchers';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { RetailRequestDataDto } from './dtos/retail-voucher.dto';

@Injectable()
export class RetailFlowService {
  private readonly logger = new Logger(RetailFlowService.name);
  private appKey: string;
  private inputGroupId: string;

  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }

  private userFlows: Record<string, any> = {};

  async dataHandling(provider: string): Promise<RetailRequestDataDto> {
    const requestData: RetailRequestDataDto = {
      packages: [],
    };

    try {
      const digitalResponse = await this.prepaidApiService.getDigital('all');
      this.logger.debug(`Received digital response for ${provider}:`, digitalResponse);

      if (digitalResponse && digitalResponse.digital) {
        // Map provider names to their API counterparts
        const providerMapping: Record<string, string[]> = {
          MAKRO: ['Makro Vouchers'],
          SORBET: ['Sorbet Digital Gift'],
          TAKEALOT: ['Takealot'],
          NETFLORIST: ['Netflorist Vouchers'],
          CYCLELAB: ['The Cycle Lab Gift Card'],
          SHOPRITE: ['Shoprite Vouchers', 'Shoprite Checkers Vouchers'],
          PNP: ['PnP Digital Voucher'],
          PROSHOP: ['The Pro Shop Gift Card'],
        };

        const searchTerms = providerMapping[provider.toUpperCase()] || [provider];
        this.logger.debug(`Using search terms for ${provider}:`, searchTerms);

        requestData.packages = digitalResponse.digital
          .filter((item) => {
            const matches = searchTerms.some(
              (term) =>
                item.product_description.toLowerCase().includes(term.toLowerCase()) ||
                item.product_provider.toLowerCase().includes(term.toLowerCase()),
            );
            this.logger.debug(`Item ${item.product_description} matches: ${matches}`);

            return matches;
          })
          .map((item) => ({
            id: item.product_code?.toString() ?? '',
            title: item.product_description,
            description: item.product_provider,
            metadata: 'R' + item.product_value,
          }));

        // Sort packages by value for better presentation
        requestData.packages.sort((a, b) => {
          const valueA = Number(a.metadata.replace(/[^\d.]/g, ''));
          const valueB = Number(b.metadata.replace(/[^\d.]/g, ''));

          return valueA - valueB;
        });
      }

      return requestData;
    } catch (error) {
      this.logger.error('Data handling failed:', error);

      return requestData;
    }
  }

  private formatBrandKey(brand: string): string {
    return brand.toUpperCase().replace(/-/g, '_').replace(/\s+/g, '_'); // Replace any spaces with underscores
  }

  async managePackages(decryptedBody: any, flowType: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;
    this.logger.debug('Received request:', { screen, action, flowType });

    // Handle ping action
    if (action === 'ping') {
      return { version, data: { status: 'active' } };
    }

    const requestData = data?.data || {};

    // Handle client error scenarios
    if (requestData?.error) {
      this.logger.warn('Retail Received client error:', requestData);

      return { version, data: { acknowledged: true } };
    }

    // Get input group values if needed
    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    // Initialize user flow state if not exists
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // Handle INIT action
    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.RETAIL_VOUCHERS,
        data: {
          ...SCREEN_RESPONSES.RETAIL_VOUCHERS.data,
          brand_list: [
            { id: 'default', title: 'Select a Brand' },
            { id: 'MAKRO', title: 'Makro' },
            { id: 'SORBET', title: 'Sorbet' },
            { id: 'TAKEALOT', title: 'Takealot' },
            { id: 'NETFLORIST', title: 'NetFlorist' },
            { id: 'CYCLELAB', title: 'Cycle Lab' },
            { id: 'SHOPRITE', title: 'Shoprite' },
            { id: 'PNP', title: 'Pick n Pay' },
            { id: 'PROSHOP', title: 'ProShop' },
          ],
        },
      };
    }

    // Handle data exchange
    if (action === 'data_exchange') {
      switch (screen) {
        case 'RETAIL_VOUCHERS': {
          const brand = requestData.brand || '';
          if (!brand || brand.toLowerCase() === 'default') {
            return {
              version,
              screen: 'RETAIL_VOUCHERS',
              data: { error: 'Please select a valid brand' },
            };
          }

          const brandKey = this.formatBrandKey(brand);
          this.logger.debug(`Processing brand: ${brandKey}`);

          const apiData = await this.dataHandling(brandKey);

          if (!SCREEN_RESPONSES[brandKey]) {
            this.logger.error(`No screen response found for ${brandKey}`);

            return {
              version,
              screen: 'RETAIL_VOUCHERS',
              data: { error: 'Invalid brand selection' },
            };
          }

          // Add 'Shoprite Own Amount' option only for Shoprite
          let productList = [
            { id: 'default', title: 'Select a Product', enabled: false },
            ...apiData.packages,
          ];
          if (brandKey === 'SHOPRITE') {
            productList = [
              { id: '0', title: 'Shoprite Own Amount', description: '(R5-R10000)', metadata: '' },
            ];
          }

          return {
            version,
            screen: brandKey,
            data: {
              brand: brand,
              product_list: productList,
            },
          };
        }

        case 'MAKRO':
        case 'SORBET':
        case 'TAKEALOT':
        case 'NETFLORIST':
        case 'CYCLELAB':
        case 'SHOPRITE':
        case 'PNP':
        case 'PROSHOP': {
          const product = requestData.products;
          if (!product || product === 'default') {
            return {
              version,
              data: { error: 'Please select a valid product' },
            };
          }

          // Special handling for Shoprite Own Amount (id: '0')
          if (screen === 'SHOPRITE' && product === '0') {
            const amount = Number(requestData.amount);
            if (isNaN(amount) || amount < 5 || amount > 10000) {
              return {
                version,
                data: { error: 'Please enter a valid amount between R5 and R10000.' },
              };
            }

            const quantity = Number(requestData.quantity) || 1;
            const cartItems = {
              uid: inputGroupValues.userDetails.uid,
              cellnumber: inputGroupValues.userDetails.cellnumber,
              service: 'Digital',
              provider: 'Shoprite',
              product_code: 'SHOPRITE_OWN_AMOUNT',
              product_name: `Shoprite R${amount}`,
              amount,
              quantity,
            };
            this.logger.debug(
              '🧺 Final cart items (Shoprite Own Amount):',
              JSON.stringify(cartItems, null, 2),
            );
            await this.chatstackSvc.addToCart(
              this.inputGroupId,
              this.appKey,
              flow_token,
              cartItems,
            );

            return {
              ...SCREEN_RESPONSES.SUCCESS,
              data: {
                ...SCREEN_RESPONSES.SUCCESS.data,
                extension_message_response: {
                  params: { flow_token },
                },
              },
            };
          }

          const apiData = await this.dataHandling(screen);
          const selectedProduct = apiData.packages.find((p) => p.id === product);

          if (!selectedProduct) {
            return {
              version,
              data: { error: 'Invalid product selection' },
            };
          }

          const quantity = Number(requestData.quantity) || 1;
          const cartItems = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Digital',
            provider: selectedProduct.description,
            product_code: selectedProduct.id,
            product_name: selectedProduct.title,
            amount: Number(selectedProduct.metadata.replace(/[^\d.]/g, '')),
            quantity,
          };

          this.logger.debug('🧺 Final cart items:', JSON.stringify(cartItems, null, 2));
          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItems);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: { flow_token },
              },
            },
          };
        }

        default:
          this.logger.error(`Unhandled screen type: ${screen}`);

          return {
            version,
            data: { error: `Unhandled screen type: ${screen}` },
          };
      }
    }

    this.logger.warn(`Unhandled action type: ${action}`);

    return {
      version,
      data: { error: `Unhandled action type: ${action}` },
    };
  }
}
