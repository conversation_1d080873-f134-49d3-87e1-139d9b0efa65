export const SCREEN_RESPONSES = {
  RETAIL_VOUCHERS: {
    version: '3.0',
    screen: 'RETAIL_VOUCHERS',
    data: {
      brand_list: [
        {
          id: 'default',
          title: 'Select a Brand',
        },
      ],
    },
  },
  MAKRO: {
    version: '3.0',
    screen: 'MAKRO',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  SORBET: {
    version: '3.0',
    screen: 'SORBET',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  TAKEALOT: {
    version: '3.0',
    screen: 'TAKEALOT',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  NETFLORIST: {
    version: '3.0',
    screen: 'NETFLORIST',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  CYCLELAB: {
    version: '3.0',
    screen: 'CYCLELAB',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  SHOPRITE: {
    version: '3.0',
    screen: 'SHOPRITE',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  PNP: {
    version: '3.0',
    screen: 'PNP',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  PROSHOP: {
    version: '3.0',
    screen: 'PROSHOP',
    data: {
      brand: '${form.brand}',
      product_list: [
        {
          id: 'default',
          title: 'Select a Product',
        },
      ],
    },
  },
  SUCCESS: {
    version: '3.0',
    screen: 'SUCCESS',
    data: {
      extension_message_response: {
        params: {
          flow_token: 'REPLACE_FLOW_TOKEN',
        },
      },
    },
  },
};
