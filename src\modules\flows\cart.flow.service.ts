import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { ChatstackService } from '../shared/chatstack.service';
import { CartItemsRequestDataDto } from './dtos/cart-items.dto';
import { SCREEN_RESPONSES } from './screenResponses/cart-items';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';

@Injectable()
export class CartFlowService {
  private readonly logger = new Logger(CartFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  constructor(
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
    private readonly prepaidApiService: PrepaidApiService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }
  private userFlows: Record<string, any> = {};

  formatAmount(amount: any) {
    return Number(amount).toLocaleString('en-ZA');
  }

  async getItems(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // Handle ping action
    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    let requestData = data?.data || data || {}; // Ensure safe data extraction

    // Handle client error scenarios
    if (requestData?.error) {
      console.warn('View cart Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      let items: CartItemsRequestDataDto = {
        items: [],
      };
      let index = 0;

      items = inputGroupValues.cart_details.map((item) => {
        const quantity = item.quantity ?? 1;
        const quantityPart = quantity > 1 ? ` x ${quantity}` : '';
        const amount = this.formatAmount(item.amount * quantity);

        return {
          id: (index++).toString(),
          title: `${item.product_name}${quantityPart} - R${amount}`,
        };
      });
      requestData = { ...requestData, items };
    }

    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.CART_VIEW,
        data: {
          ...SCREEN_RESPONSES.CART_VIEW.data,
          cart_items: [...SCREEN_RESPONSES.CART_VIEW.data.cart_items, ...requestData.items],
        },
      };
    }

    if (!screen) {
      console.error('Error: View cart Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    // Handle screen transitions
    if (action === 'data_exchange') {
      switch (screen) {
        case 'CART_VIEW':
          const removeItemIds = requestData.remove_item_id || [];
          this.logger.debug('Items to remove:', removeItemIds);
          this.logger.debug(
            'Current cart:',
            JSON.stringify(inputGroupValues.cart_details, null, 2),
          );

          // Convert removeItemIds to a Set of integers for easier lookup
          const itemsToRemove = new Set(removeItemIds.map((id) => parseInt(id)));
          const cartDetails = inputGroupValues.cart_details || [];

          // Find electricity and arrears items
          const arrearsItems = cartDetails.filter((item) => item.product_name?.includes('Arrears'));

          const electricityItems = cartDetails.filter((item) => item.service === 'Electricity');

          // Check if any arrears items are being removed
          const arrearsBeingRemoved = arrearsItems.some((item) =>
            itemsToRemove.has(cartDetails.indexOf(item)),
          );

          // If arrears are being removed, we need to check relationships with electricity items
          if (arrearsBeingRemoved && electricityItems.length > 0) {
            try {
              // Get the cellnumber from the cart items
              const cellNumber = cartDetails[0]?.cellnumber;

              if (cellNumber) {
                // Get meters data to determine relationships
                const metersResponse = await this.prepaidApiService.getMeters(cellNumber);
                this.logger.debug('Meters response:', JSON.stringify(metersResponse, null, 2));

                if (metersResponse && metersResponse.meters) {
                  // Create maps for relationships
                  const accountToMeters = new Map();
                  const meterToAccounts = new Map();
                  const meterToMunicipality = new Map();

                  // Build relationship maps
                  metersResponse.meters.forEach((meter) => {
                    // Store municipality for each meter
                    meterToMunicipality.set(meter.number, meter.municipal_name);

                    if (meter.primary_account) {
                      // Map account to meters
                      if (!accountToMeters.has(meter.primary_account)) {
                        accountToMeters.set(meter.primary_account, []);
                      }

                      accountToMeters.get(meter.primary_account).push(meter.number);

                      // Map meter to account
                      meterToAccounts.set(meter.number, meter.primary_account);
                    }
                  });

                  this.logger.debug(
                    'Account to meters map:',
                    JSON.stringify(Array.from(accountToMeters.entries()), null, 2),
                  );
                  this.logger.debug(
                    'Meter to accounts map:',
                    JSON.stringify(Array.from(meterToAccounts.entries()), null, 2),
                  );
                  this.logger.debug(
                    'Meter to municipality map:',
                    JSON.stringify(Array.from(meterToMunicipality.entries()), null, 2),
                  );

                  // For each arrears item being removed, find related electricity items
                  for (let i = 0; i < arrearsItems.length; i++) {
                    const arrearsItem = arrearsItems[i];
                    const arrearsIndex = cartDetails.indexOf(arrearsItem);

                    if (itemsToRemove.has(arrearsIndex)) {
                      const arrearsNumber = arrearsItem.number;
                      const arrearsProvider = arrearsItem.provider;

                      this.logger.debug(
                        `Checking relationships for arrears item: ${arrearsNumber} from provider ${arrearsProvider}`,
                      );

                      // Find related meters for this account
                      const relatedMeters = accountToMeters.get(arrearsNumber) || [];

                      this.logger.debug(
                        `Related meters for account ${arrearsNumber}:`,
                        relatedMeters,
                      );

                      // Mark all electricity items with related meters for removal
                      electricityItems.forEach((electricityItem) => {
                        const electricityNumber = electricityItem.number;
                        const electricityProvider = electricityItem.provider;
                        const electricityIndex = cartDetails.indexOf(electricityItem);

                        this.logger.debug(
                          `Checking electricity item ${electricityNumber} from provider ${electricityProvider}`,
                        );

                        // Check relationship based on meter API data
                        const isRelatedByAccount =
                          relatedMeters.includes(electricityNumber) ||
                          meterToAccounts.get(electricityNumber) === arrearsNumber;

                        // Check relationship based on provider and municipality
                        const electricityMunicipality = meterToMunicipality.get(electricityNumber);
                        const isSameProvider = arrearsProvider === electricityProvider;

                        this.logger.debug(`Electricity municipality: ${electricityMunicipality}`);
                        this.logger.debug(
                          `Is related by account: ${isRelatedByAccount}, Is same provider: ${isSameProvider}`,
                        );

                        // Only remove if directly related by account/meter
                        if (isRelatedByAccount) {
                          this.logger.debug(
                            `Marking electricity item ${electricityNumber} for removal because it's directly related to arrears ${arrearsNumber}`,
                          );
                          itemsToRemove.add(electricityIndex);
                        }
                      });
                    }
                  }
                }
              }
            } catch (error) {
              this.logger.error('Error fetching meters data:', error);
            }
          }

          this.logger.debug('Final items to remove:', Array.from(itemsToRemove));

          // Filter out items that should be removed
          const updatedCart = cartDetails.filter((_, index) => !itemsToRemove.has(index));

          this.logger.debug('Updated cart:', JSON.stringify(updatedCart, null, 2));

          await this.chatstackSvc.saveToInputGroup(this.inputGroupId, this.appKey, flow_token, {
            cart_details: updatedCart,
          });

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: {
                  flow_token,
                },
              },
            },
          };

        default:
          throw new Error('Manage cart Unhandled screen type.');
      }
    }
  }
}
