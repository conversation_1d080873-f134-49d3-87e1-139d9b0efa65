import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { ChatstackService } from '../shared/chatstack.service';
import { SCREEN_RESPONSES } from './screenResponses/googleplay';

@Injectable()
export class GooglePlayFlowService {
  private readonly logger = new Logger(GooglePlayFlowService.name);
  private appKey: string;
  private inputGroupId: string;

  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }

  private userFlows: Record<string, any> = {};

  async getNextScreen(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    let requestData = data?.data || data || {};

    if (requestData?.error) {
      console.warn('Google Play Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      requestData = { ...requestData, ...decryptedBody.data };
    }

    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.GOOGLEPLAY,
        data: {
          ...SCREEN_RESPONSES.GOOGLEPLAY.data,
        },
      };
    }

    if (!screen) {
      console.error('❌ Error: Google Play Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    if (action === 'data_exchange') {
      switch (screen) {
        case 'GOOGLEPLAY': {
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            amount: requestData.amount ?? null,
            quantity: requestData.quantity ?? 1,
          };

          const flowData = this.userFlows[flow_token];

          const digitalResponse = await this.prepaidApiService.getDigital('Google Play Gift Codes');

          if (digitalResponse.result !== 'Success' || !digitalResponse.digital.length) {
            return {
              ...SCREEN_RESPONSES.GOOGLEPLAY,
              data: {
                ...SCREEN_RESPONSES.GOOGLEPLAY.data,
                error: 'Google Play products not found',
              },
            };
          }

          const googlePlayProducts = digitalResponse.digital;

          const cartItem = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Digital',
            provider: 'Google Play Gift Codes',
            amount: Number(flowData.amount),
            product_code: googlePlayProducts[0].product_code,
            product_name: googlePlayProducts[0].product_description,
            quantity: flowData.quantity,
          };

          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItem);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: { flow_token },
              },
            },
          };
        }

        default:
          throw new Error('❌ Google Play Unhandled screen type.');
      }
    }
  }
}
