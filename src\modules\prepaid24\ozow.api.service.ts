import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class OzowApiService {
  private readonly logger = new Logger(OzowApiService.name);
  private baseUrl: string;
  private apiUser: string;
  private apiPassword: string;
  private token: string;

  constructor(private readonly configService: ConfigService) {
    // this.baseUrl = 'https://pay24.hmdev.cloud';
    this.baseUrl = 'https://testing04.prepaid24.co.za';
    this.apiUser = 'APIUSRR';
    this.apiPassword = 'g@z7ah#nUez68$@';
    this.logger.debug(`Loaded API URL: ${this.baseUrl}`);
  }

  private async generateToken(): Promise<string> {
    const options = {
      method: 'GET',
      url: this.baseUrl,
      headers: {
        'API-USER': this.apiUser,
        'API-PASS': this.apiPassword,
      },
    };
    try {
      const response = await axios.request(options);
      this.logger.log('Token: ', response);
      this.token = response.data.api_token;

      return this.token;
    } catch (error: any) {
      this.logger.error('Failed to retrieve generateToken request: ', {
        error: error.message,
        stack: error.stack,
        response: error.response?.data,
      });
      throw new BadRequestException(`There is some error generated while generating token.`, error);
    }
  }

  async getToken(): Promise<string> {
    if (!this.token) {
      this.token = await this.generateToken();
    }

    return this.token;
  }
}
