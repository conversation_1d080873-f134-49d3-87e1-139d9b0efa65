import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SCREEN_RESPONSES } from './screenResponses/netflix';
import { PrepaidApiService } from '../prepaid24/prepaid24.api.service';
import { DigitalDto, NetflixRequestDataDto } from './dtos/netflix.dto';
import { ChatstackService } from '../shared/chatstack.service';

@Injectable()
export class NetflixFlowService {
  private readonly logger = new Logger(NetflixFlowService.name);
  private appKey: string;
  private inputGroupId: string;
  private userFlows: Record<string, any> = {};
  private productCodeMap: string = '';

  constructor(
    private readonly prepaidApiService: PrepaidApiService,
    private readonly chatstackSvc: ChatstackService,
    private readonly configService: ConfigService,
  ) {
    this.appKey = this.configService.get('APP_KEY');
    this.inputGroupId = this.configService.get('INPUT_GROUP_ID');
  }

  async dataHandling(): Promise<NetflixRequestDataDto> {
    const requestData: NetflixRequestDataDto = {
      netflixProducts: [],
    };

    try {
      const digitalResponse: {
        result: string;
        digital: DigitalDto[];
      } = await this.prepaidApiService.getDigital('sub');

      let index = 1;

      if (digitalResponse && digitalResponse.digital) {
        requestData.netflixProducts = digitalResponse.digital.map((netflix) => {
          const baseCode = digitalResponse.digital[0].product_code?.toString() ?? '';
          const id = `${index++}`; // Append index to product_code

          this.productCodeMap = baseCode;

          return {
            id,
            title: netflix.product_description,
            description: netflix.product_provider,
            metadata: netflix.product_value,
          };
        });
      } else {
        console.error('API Error or Invalid Response:', digitalResponse);
      }

      return requestData;
    } catch (error) {
      console.error('Data handling failed:', error);

      return requestData;
    }
  }

  async getNextScreen(decryptedBody: any): Promise<any> {
    const { screen, data, version, action, flow_token } = decryptedBody;

    // ✅ Handle ping action
    if (action === 'ping') {
      return {
        version,
        data: { status: 'active' },
      };
    }

    let requestData = data?.data || data || {}; // Ensure safe data extraction

    // ✅ Handle client error scenarios
    if (requestData?.error) {
      console.warn('Received client error:', requestData);

      return {
        version,
        data: { acknowledged: true },
      };
    }

    const inputGroupValues = await this.chatstackSvc.retrieveFromInputGroup(
      this.inputGroupId,
      this.appKey,
      flow_token,
    );

    if (decryptedBody.data) {
      const apiData = await this.dataHandling();
      requestData = { ...requestData, ...apiData };
    }

    // ✅ Ensure INIT always sends a valid screen
    if (action === 'INIT') {
      return {
        ...SCREEN_RESPONSES.NETFLIX,
        data: {
          ...SCREEN_RESPONSES.NETFLIX.data,
          product_list: [
            ...SCREEN_RESPONSES.NETFLIX.data.product_list,
            { id: '0', title: 'Netflix Own Amount', description: '(R250-R1000)' },
            ...requestData.netflixProducts,
          ],
        },
      };
    }

    if (!screen) {
      console.error('❌ Error: Missing or empty screen value in request.');

      return {
        version,
        data: { error: 'Invalid request: screen is missing or empty.' },
      };
    }

    // ✅ Ensure a user flow exists for the current session
    if (!this.userFlows[flow_token]) {
      this.userFlows[flow_token] = {};
    }

    // ✅ Handle screen transitions
    if (action === 'data_exchange') {
      switch (screen) {
        case 'NETFLIX':
          this.userFlows[flow_token] = {
            ...this.userFlows[flow_token],
            products: requestData.products ?? null,
            amount: requestData.amount ?? null,
            quantity: requestData.quantity ?? 1,
          };

          this.logger.debug('🧾 requestData:', JSON.stringify(requestData));

          const quantity = Number(requestData.quantity) || 1;
          const selectedProductId = requestData.products;

          // Handle special product manually
          const isSpecialProduct = selectedProductId === '0';

          let product_code: string;
          let amount: number;
          let product_name: string;

          if (isSpecialProduct) {
            product_code = this.productCodeMap;
            amount = Number(requestData.amount);
            product_name = `Netflix R${amount}`;
          } else {
            const selectedProduct = requestData.netflixProducts?.find(
              (p) => p.id === selectedProductId,
            );

            if (!selectedProduct) {
              throw new Error(`Invalid product selection: ${selectedProductId}`);
            }

            product_code = this.productCodeMap;
            amount = Number(selectedProduct.metadata);
            product_name = selectedProduct.title;
          }

          const cartItems = {
            uid: inputGroupValues.userDetails.uid,
            cellnumber: inputGroupValues.userDetails.cellnumber,
            service: 'Digital',
            provider: 'Sub',
            amount,
            product_code,
            product_name,
            quantity,
          };

          // 🚀 Single call to save all items
          this.logger.debug('🧺 Final cart items:', JSON.stringify(cartItems, null, 2));
          await this.chatstackSvc.addToCart(this.inputGroupId, this.appKey, flow_token, cartItems);

          return {
            ...SCREEN_RESPONSES.SUCCESS,
            data: {
              ...SCREEN_RESPONSES.SUCCESS.data,
              extension_message_response: {
                params: {
                  flow_token,
                },
              },
            },
          };

        default:
          throw new Error('❌ Unhandled screen type.');
      }
    }
  }
}
