{"version": "7.0", "data_api_version": "3.0", "routing_model": {"REGISTER": ["REGISTER_AFRIKAANS"], "REGISTER_AFRIKAANS": []}, "screens": [{"id": "REGISTER", "title": "Register", "terminal": true, "success": true, "data": {"municipality_list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}}}, "__example__": []}}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextCaption", "text": "Language Preference"}, {"type": "Dropdown", "label": "Language", "name": "language", "required": true, "init-value": "english", "data-source": [{"id": "english", "title": "English"}, {"id": "afrikaans", "title": "Afrikaans"}], "on-select-action": {"name": "data_exchange", "payload": {"trigger": "language_selected", "language": "${form.language}"}}}, {"type": "TextCaption", "text": "Sign Up"}, {"type": "TextInput", "name": "name", "label": "Name", "input-type": "text", "required": true}, {"type": "TextInput", "name": "surname", "label": "Surname", "input-type": "text", "required": true}, {"type": "TextInput", "name": "cellnumber", "label": "Cell Number", "input-type": "number", "required": true}, {"type": "TextInput", "label": "Email", "name": "email", "input-type": "email", "required": true}, {"type": "Dropdown", "label": "Select Municipality", "required": false, "name": "municipality", "data-source": "${data.municipality_list}"}, {"type": "TextInput", "required": true, "label": "Set Password", "input-type": "password", "name": "password"}, {"type": "EmbeddedLink", "text": "Terms & Conditions", "on-click-action": {"name": "open_url", "url": "https://www.prepaid24.co.za/page/terms-conditions"}}, {"type": "CheckboxGroup", "name": "terms", "required": true, "label": "  ______________________________________________", "data-source": [{"id": "yes", "title": "I Accept"}]}, {"type": "Footer", "label": "Sign Up", "on-click-action": {"name": "data_exchange", "payload": {"action": "data_exchange", "screen": "REGISTER", "data": {"language": "${form.language}", "name": "${form.name}", "surname": "${form.surname}", "cellnumber": "${form.cellnumber}", "email": "${form.email}", "municipality": "${form.municipality}", "password": "${form.password}", "terms": "${form.terms}"}}}}]}}, {"id": "REGISTER_AFRIKAANS", "title": "Registreer", "terminal": true, "success": true, "data": {"municipality_list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}}}, "__example__": []}}, "layout": {"type": "SingleColumnLayout", "children": [{"type": "TextCaption", "text": "Taalvoorkeur"}, {"type": "Dropdown", "label": "Taal", "name": "language", "required": true, "init-value": "afrikaans", "data-source": [{"id": "english", "title": "English"}, {"id": "afrikaans", "title": "Afrikaans"}], "on-select-action": {"name": "data_exchange", "payload": {"trigger": "language_selected", "language": "${form.language}"}}}, {"type": "TextCaption", "text": "<PERSON><PERSON>"}, {"type": "TextInput", "name": "name", "label": "<PERSON><PERSON>", "input-type": "text", "required": true}, {"type": "TextInput", "name": "surname", "label": "<PERSON>", "input-type": "text", "required": true}, {"type": "TextInput", "name": "cellnumber", "label": "<PERSON><PERSON><PERSON><PERSON>", "input-type": "number", "required": true}, {"type": "TextInput", "label": "E-pos", "name": "email", "input-type": "email", "required": true}, {"type": "Dropdown", "label": "<PERSON><PERSON>", "required": true, "name": "municipality", "data-source": "${data.municipality_list}"}, {"type": "TextInput", "required": true, "label": "Stel Wagwoord", "input-type": "password", "name": "password"}, {"type": "EmbeddedLink", "text": "Terme & Voorwaardes", "on-click-action": {"name": "open_url", "url": "https://www.prepaid24.co.za/page/terms-conditions"}}, {"type": "CheckboxGroup", "name": "terms", "required": true, "label": "  ______________________________________________", "data-source": [{"id": "yes", "title": "Ek <PERSON>"}]}, {"type": "Footer", "label": "<PERSON><PERSON>", "on-click-action": {"name": "data_exchange", "payload": {"action": "data_exchange", "screen": "REGISTER_AFRIKAANS", "data": {"language": "${form.language}", "name": "${form.name}", "surname": "${form.surname}", "cellnumber": "${form.cellnumber}", "email": "${form.email}", "municipality": "${form.municipality}", "password": "${form.password}", "terms": "${form.terms}"}}}}]}}]}