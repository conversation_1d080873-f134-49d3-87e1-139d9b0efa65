import { forwardRef, Module } from '@nestjs/common';
import { EncryptionModule } from 'src/encryption/encryption.module';

import { AirtimeDataFlowService } from './airtime-data.flow.service';
import { CartFlowService } from './cart.flow.service';
import { DigitalFlowService } from './digital.flow.service';
import { DstvFlowService } from './dstv-flow.service';
import { EntertainmentFlowService } from './entertainment.flow.service';
import { GamingFlowService } from './gaming-voucher.flow.service';
import { GooglePlayFlowService } from './googleplay.flow.service';
import { MunicipalFlowService } from './municipal.flow.service';
import { NetflixFlowService } from './netflix.flow.service';
import { PrepaidFlowService } from './prepaid.flow.service';
import { RetailFlowService } from './retail-voucher.flow.service';
import { EncryptionService } from '../../encryption/encryption.service';
import { Prepaid24Module } from '../prepaid24/prepaid24.module'; // Ensure this is correct
import { ChatstackService } from '../shared/chatstack.service';
import { RegisterFlowService } from './register.flow.service';

@Module({
  imports: [Prepaid24Module, forwardRef(() => Prepaid24Module), EncryptionModule], // Ensure it exists
  providers: [
    PrepaidFlowService,
    EncryptionService,
    NetflixFlowService,
    MunicipalFlowService,
    AirtimeDataFlowService,
    GooglePlayFlowService,
    ChatstackService,
    CartFlowService,
    DigitalFlowService,
    DstvFlowService,
    EntertainmentFlowService,
    GamingFlowService,
    RetailFlowService,
    RegisterFlowService,
  ],
  exports: [
    PrepaidFlowService,
    NetflixFlowService,
    MunicipalFlowService,
    CartFlowService,
    DigitalFlowService,
    AirtimeDataFlowService,
    GooglePlayFlowService,
    DstvFlowService,
    EntertainmentFlowService,
    GamingFlowService,
    RetailFlowService,
    RegisterFlowService,
  ],
})
export class FlowModule {}
